#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于SHAP的智能特征选择系统
替代或补充传统的RFE方法，提供更稳健的特征选择
"""

import json
import os
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class SHAPFeatureSelector:
    """基于SHAP值的智能特征选择器"""
    
    def __init__(self, min_importance_threshold: float = 0.001, 
                 top_k_features: Optional[int] = None,
                 importance_percentile: float = 0.1):
        """
        初始化SHAP特征选择器
        
        Args:
            min_importance_threshold: 最小重要性阈值，低于此值的特征将被过滤
            top_k_features: 保留前K个最重要的特征，None表示不限制
            importance_percentile: 重要性百分位数阈值，低于此百分位数的特征将被过滤
        """
        self.min_importance_threshold = min_importance_threshold
        self.top_k_features = top_k_features
        self.importance_percentile = importance_percentile
        
        # 特征选择历史
        self.selection_history: List[Dict] = []
        
        # 当前选择的特征
        self.selected_features: List[str] = []
        self.feature_importance_scores: Dict[str, float] = {}
        
        logger.info(f"[SHAPFeatureSelector] 初始化完成，阈值={min_importance_threshold}, top_k={top_k_features}, 百分位数={importance_percentile}")
    
    def extract_feature_importance_from_shap(self, shap_analysis_results: Dict) -> Dict[str, float]:
        """
        从SHAP分析结果中提取特征重要性
        
        Args:
            shap_analysis_results: perform_shap_analysis函数的返回结果
            
        Returns:
            特征重要性字典 {feature_name: importance_score}
        """
        if not shap_analysis_results or 'feature_importance' not in shap_analysis_results:
            logger.warning("[SHAPFeatureSelector] SHAP分析结果为空或缺少feature_importance")
            return {}
        
        feature_importance = shap_analysis_results['feature_importance']
        
        # 转换为字典格式
        importance_dict = {}
        for item in feature_importance:
            if isinstance(item, (list, tuple)) and len(item) >= 2:
                feature_name, importance_score = item[0], float(item[1])
                importance_dict[feature_name] = importance_score
        
        logger.info(f"[SHAPFeatureSelector] 从SHAP结果中提取了 {len(importance_dict)} 个特征的重要性")
        return importance_dict
    
    def load_feature_importance_from_file(self, shap_json_path: str) -> Dict[str, float]:
        """
        从SHAP分析JSON文件中加载特征重要性
        
        Args:
            shap_json_path: SHAP分析结果JSON文件路径
            
        Returns:
            特征重要性字典
        """
        try:
            if not os.path.exists(shap_json_path):
                logger.warning(f"[SHAPFeatureSelector] SHAP文件不存在: {shap_json_path}")
                return {}
            
            with open(shap_json_path, 'r', encoding='utf-8') as f:
                shap_data = json.load(f)
            
            return self.extract_feature_importance_from_shap(shap_data)
            
        except Exception as e:
            logger.error(f"[SHAPFeatureSelector] 加载SHAP文件失败: {e}")
            return {}
    
    def select_features(self, feature_importance: Dict[str, float], 
                       all_features: List[str]) -> Tuple[List[str], Dict]:
        """
        基于SHAP重要性选择特征
        
        Args:
            feature_importance: 特征重要性字典
            all_features: 所有可用特征列表
            
        Returns:
            (选择的特征列表, 选择统计信息)
        """
        if not feature_importance:
            logger.warning("[SHAPFeatureSelector] 特征重要性为空，返回所有特征")
            return all_features, {"method": "fallback", "reason": "empty_importance"}
        
        # 确保所有特征都有重要性分数（缺失的设为0）
        complete_importance = {}
        for feature in all_features:
            complete_importance[feature] = feature_importance.get(feature, 0.0)
        
        # 按重要性排序
        sorted_features = sorted(complete_importance.items(), key=lambda x: x[1], reverse=True)
        
        # 应用选择策略
        selected_features = []
        selection_stats = {
            "total_features": len(all_features),
            "method": "shap_based",
            "criteria": []
        }
        
        # 策略1: 最小重要性阈值
        threshold_filtered = [(name, score) for name, score in sorted_features 
                             if score >= self.min_importance_threshold]
        selection_stats["criteria"].append(f"min_threshold_{self.min_importance_threshold}")
        selection_stats["after_threshold"] = len(threshold_filtered)
        
        # 策略2: 百分位数过滤
        if threshold_filtered:
            importance_values = [score for _, score in threshold_filtered]
            percentile_threshold = np.percentile(importance_values, self.importance_percentile * 100)
            percentile_filtered = [(name, score) for name, score in threshold_filtered 
                                 if score >= percentile_threshold]
            selection_stats["criteria"].append(f"percentile_{self.importance_percentile}")
            selection_stats["after_percentile"] = len(percentile_filtered)
            selection_stats["percentile_threshold"] = percentile_threshold
        else:
            percentile_filtered = threshold_filtered
            selection_stats["after_percentile"] = 0
        
        # 策略3: Top-K限制
        if self.top_k_features and len(percentile_filtered) > self.top_k_features:
            final_filtered = percentile_filtered[:self.top_k_features]
            selection_stats["criteria"].append(f"top_k_{self.top_k_features}")
            selection_stats["after_top_k"] = len(final_filtered)
        else:
            final_filtered = percentile_filtered
            selection_stats["after_top_k"] = len(final_filtered)
        
        # 提取最终选择的特征
        selected_features = [name for name, score in final_filtered]
        
        # 保存重要性分数
        self.feature_importance_scores = {name: score for name, score in final_filtered}
        self.selected_features = selected_features
        
        # 记录选择历史
        selection_record = {
            "timestamp": pd.Timestamp.now().isoformat(),
            "selected_features": selected_features,
            "selection_stats": selection_stats,
            "top_10_features": [(name, score) for name, score in sorted_features[:10]]
        }
        self.selection_history.append(selection_record)
        
        logger.info(f"[SHAPFeatureSelector] 特征选择完成: {len(all_features)} -> {len(selected_features)}")
        logger.info(f"[SHAPFeatureSelector] 选择策略: {', '.join(selection_stats['criteria'])}")
        
        return selected_features, selection_stats
    
    def get_feature_importance_summary(self) -> Dict:
        """获取特征重要性摘要"""
        if not self.feature_importance_scores:
            return {}
        
        sorted_features = sorted(self.feature_importance_scores.items(), 
                               key=lambda x: x[1], reverse=True)
        
        return {
            "total_selected_features": len(sorted_features),
            "top_10_features": sorted_features[:10],
            "importance_range": {
                "max": max(self.feature_importance_scores.values()),
                "min": min(self.feature_importance_scores.values()),
                "mean": np.mean(list(self.feature_importance_scores.values())),
                "std": np.std(list(self.feature_importance_scores.values()))
            }
        }
    
    def save_selection_results(self, save_path: str) -> None:
        """保存特征选择结果"""
        try:
            results = {
                "selector_config": {
                    "min_importance_threshold": self.min_importance_threshold,
                    "top_k_features": self.top_k_features,
                    "importance_percentile": self.importance_percentile
                },
                "selected_features": self.selected_features,
                "feature_importance_scores": self.feature_importance_scores,
                "selection_history": self.selection_history,
                "summary": self.get_feature_importance_summary()
            }
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"[SHAPFeatureSelector] 选择结果已保存: {save_path}")
            
        except Exception as e:
            logger.error(f"[SHAPFeatureSelector] 保存选择结果失败: {e}")
    
    def load_selection_results(self, load_path: str) -> bool:
        """加载特征选择结果"""
        try:
            if not os.path.exists(load_path):
                logger.warning(f"[SHAPFeatureSelector] 选择结果文件不存在: {load_path}")
                return False
            
            with open(load_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 恢复配置
            config = results.get("selector_config", {})
            self.min_importance_threshold = config.get("min_importance_threshold", self.min_importance_threshold)
            self.top_k_features = config.get("top_k_features", self.top_k_features)
            self.importance_percentile = config.get("importance_percentile", self.importance_percentile)
            
            # 恢复选择结果
            self.selected_features = results.get("selected_features", [])
            self.feature_importance_scores = results.get("feature_importance_scores", {})
            self.selection_history = results.get("selection_history", [])
            
            logger.info(f"[SHAPFeatureSelector] 选择结果已加载: {load_path}")
            logger.info(f"[SHAPFeatureSelector] 加载了 {len(self.selected_features)} 个选择的特征")
            
            return True
            
        except Exception as e:
            logger.error(f"[SHAPFeatureSelector] 加载选择结果失败: {e}")
            return False


def create_shap_feature_selector_from_config(config_dict: Dict) -> SHAPFeatureSelector:
    """从配置字典创建SHAP特征选择器"""
    return SHAPFeatureSelector(
        min_importance_threshold=config_dict.get("min_importance_threshold", 0.001),
        top_k_features=config_dict.get("top_k_features", None),
        importance_percentile=config_dict.get("importance_percentile", 0.1)
    )


def apply_shap_feature_selection(X_df: pd.DataFrame, 
                                shap_analysis_results: Dict,
                                selector_config: Dict = None) -> Tuple[pd.DataFrame, List[str], Dict]:
    """
    应用SHAP特征选择到数据集
    
    Args:
        X_df: 特征数据DataFrame
        shap_analysis_results: SHAP分析结果
        selector_config: 选择器配置
        
    Returns:
        (过滤后的DataFrame, 选择的特征列表, 选择统计信息)
    """
    if selector_config is None:
        selector_config = {}
    
    # 创建选择器
    selector = create_shap_feature_selector_from_config(selector_config)
    
    # 提取特征重要性
    feature_importance = selector.extract_feature_importance_from_shap(shap_analysis_results)
    
    # 选择特征
    selected_features, selection_stats = selector.select_features(
        feature_importance, list(X_df.columns)
    )
    
    # 过滤数据
    if selected_features:
        X_filtered = X_df[selected_features].copy()
    else:
        logger.warning("没有选择到任何特征，返回原始数据")
        X_filtered = X_df.copy()
        selected_features = list(X_df.columns)
    
    return X_filtered, selected_features, selection_stats

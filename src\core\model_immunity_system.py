#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型免疫系统 - 自动重训练与验证
基于性能衰退和数据漂移检测的智能重训练触发器
"""

import json
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import threading
import logging
from scipy import stats
from collections import deque
import warnings

logger = logging.getLogger(__name__)


class DataDriftDetector:
    """数据漂移检测器"""
    
    def __init__(self, key_features: List[str] = None, 
                 ks_threshold: float = 0.05, psi_threshold: float = 0.2):
        """
        初始化数据漂移检测器
        
        Args:
            key_features: 关键特征列表，用于漂移检测
            ks_threshold: KS检验的p值阈值，低于此值认为有显著漂移
            psi_threshold: PSI阈值，高于此值认为有显著漂移
        """
        self.key_features = key_features or [
            'global_atr_percent', 'meta_prob_diff_up_vs_down', 'global_trend_strength',
            'global_adx', 'global_ema_short', 'global_ema_long', 'volume_x_price_change',
            'rsi_velocity', 'bb_width_x_volume_anomaly'
        ]
        self.ks_threshold = ks_threshold
        self.psi_threshold = psi_threshold
        
        # 训练时的特征分布基准
        self.baseline_distributions: Dict[str, Dict] = {}
        
        # 实时数据窗口
        self.realtime_window_size = 1000  # 最近1000个样本
        self.realtime_data_windows: Dict[str, deque] = {}
        
        # 漂移检测历史
        self.drift_history: List[Dict] = []
        
        logger.info(f"[DataDriftDetector] 初始化完成，监控特征: {len(self.key_features)}个")
    
    def save_baseline_distributions(self, training_data: pd.DataFrame, 
                                  save_path: str = "baseline_distributions.json"):
        """
        保存训练数据的基准分布
        
        Args:
            training_data: 训练数据DataFrame
            save_path: 保存路径
        """
        try:
            baseline_stats = {}
            
            for feature in self.key_features:
                if feature in training_data.columns:
                    feature_data = training_data[feature].dropna()
                    
                    if len(feature_data) > 0:
                        baseline_stats[feature] = {
                            'mean': float(feature_data.mean()),
                            'std': float(feature_data.std()),
                            'min': float(feature_data.min()),
                            'max': float(feature_data.max()),
                            'q25': float(feature_data.quantile(0.25)),
                            'q50': float(feature_data.quantile(0.50)),
                            'q75': float(feature_data.quantile(0.75)),
                            'count': int(len(feature_data)),
                            'percentiles': [float(feature_data.quantile(p/100)) for p in range(0, 101, 5)]
                        }
                    else:
                        logger.warning(f"特征 {feature} 无有效数据")
                else:
                    logger.warning(f"特征 {feature} 不存在于训练数据中")
            
            # 保存基准分布
            self.baseline_distributions = baseline_stats
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'baseline_distributions': baseline_stats,
                    'creation_time': datetime.now().isoformat(),
                    'sample_count': len(training_data),
                    'feature_count': len(baseline_stats)
                }, f, indent=2, ensure_ascii=False)
            
            logger.info(f"[DataDriftDetector] 基准分布已保存: {save_path}")
            logger.info(f"[DataDriftDetector] 保存了 {len(baseline_stats)} 个特征的分布")
            
        except Exception as e:
            logger.error(f"[DataDriftDetector] 保存基准分布失败: {e}")
    
    def load_baseline_distributions(self, load_path: str = "baseline_distributions.json") -> bool:
        """
        加载基准分布
        
        Args:
            load_path: 加载路径
            
        Returns:
            是否加载成功
        """
        try:
            if not os.path.exists(load_path):
                logger.warning(f"[DataDriftDetector] 基准分布文件不存在: {load_path}")
                return False
            
            with open(load_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.baseline_distributions = data.get('baseline_distributions', {})
            
            logger.info(f"[DataDriftDetector] 基准分布已加载: {load_path}")
            logger.info(f"[DataDriftDetector] 加载了 {len(self.baseline_distributions)} 个特征的分布")
            
            return True
            
        except Exception as e:
            logger.error(f"[DataDriftDetector] 加载基准分布失败: {e}")
            return False
    
    def update_realtime_data(self, new_data: pd.DataFrame):
        """
        更新实时数据窗口
        
        Args:
            new_data: 新的实时数据
        """
        for feature in self.key_features:
            if feature in new_data.columns:
                if feature not in self.realtime_data_windows:
                    self.realtime_data_windows[feature] = deque(maxlen=self.realtime_window_size)
                
                # 添加新数据
                feature_values = new_data[feature].dropna().values
                for value in feature_values:
                    self.realtime_data_windows[feature].append(float(value))
    
    def calculate_psi(self, baseline_data: np.ndarray, current_data: np.ndarray, 
                     bins: int = 10) -> float:
        """
        计算Population Stability Index (PSI)
        
        Args:
            baseline_data: 基准数据
            current_data: 当前数据
            bins: 分箱数量
            
        Returns:
            PSI值
        """
        try:
            # 确保数据有效
            baseline_data = baseline_data[~np.isnan(baseline_data)]
            current_data = current_data[~np.isnan(current_data)]
            
            if len(baseline_data) == 0 or len(current_data) == 0:
                return float('inf')
            
            # 基于基准数据创建分箱
            _, bin_edges = np.histogram(baseline_data, bins=bins)
            
            # 计算基准和当前数据的分布
            baseline_counts, _ = np.histogram(baseline_data, bins=bin_edges)
            current_counts, _ = np.histogram(current_data, bins=bin_edges)
            
            # 转换为概率分布
            baseline_probs = baseline_counts / len(baseline_data)
            current_probs = current_counts / len(current_data)
            
            # 避免零概率（加小常数）
            baseline_probs = np.where(baseline_probs == 0, 1e-6, baseline_probs)
            current_probs = np.where(current_probs == 0, 1e-6, current_probs)
            
            # 计算PSI
            psi = np.sum((current_probs - baseline_probs) * np.log(current_probs / baseline_probs))
            
            return float(psi)
            
        except Exception as e:
            logger.warning(f"计算PSI时出错: {e}")
            return float('inf')
    
    def detect_drift(self, verbose: bool = False) -> Dict[str, Any]:
        """
        检测数据漂移
        
        Args:
            verbose: 是否输出详细信息
            
        Returns:
            漂移检测结果
        """
        if not self.baseline_distributions:
            logger.warning("[DataDriftDetector] 未加载基准分布，无法检测漂移")
            return {'error': 'no_baseline'}
        
        drift_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_drift_detected': False,
            'feature_results': {},
            'summary': {
                'total_features': 0,
                'drifted_features_ks': 0,
                'drifted_features_psi': 0,
                'avg_ks_pvalue': 0.0,
                'avg_psi_score': 0.0
            }
        }
        
        ks_pvalues = []
        psi_scores = []
        
        for feature in self.key_features:
            if feature not in self.baseline_distributions:
                continue
            
            if feature not in self.realtime_data_windows or len(self.realtime_data_windows[feature]) < 50:
                if verbose:
                    logger.info(f"特征 {feature} 实时数据不足，跳过漂移检测")
                continue
            
            # 获取基准数据和当前数据
            baseline_stats = self.baseline_distributions[feature]
            current_data = np.array(list(self.realtime_data_windows[feature]))
            
            # 重构基准数据（使用百分位数近似）
            percentiles = baseline_stats.get('percentiles', [])
            if len(percentiles) >= 20:  # 至少有足够的百分位数
                baseline_data = np.array(percentiles)
            else:
                # 使用正态分布近似
                mean = baseline_stats['mean']
                std = baseline_stats['std']
                baseline_data = np.random.normal(mean, std, 1000)
            
            # KS检验
            try:
                ks_statistic, ks_pvalue = stats.ks_2samp(baseline_data, current_data)
                ks_drift = ks_pvalue < self.ks_threshold
            except Exception as e:
                logger.warning(f"KS检验失败 ({feature}): {e}")
                ks_statistic, ks_pvalue, ks_drift = 0.0, 1.0, False
            
            # PSI计算
            psi_score = self.calculate_psi(baseline_data, current_data)
            psi_drift = psi_score > self.psi_threshold
            
            # 记录结果
            feature_result = {
                'ks_statistic': float(ks_statistic),
                'ks_pvalue': float(ks_pvalue),
                'ks_drift': ks_drift,
                'psi_score': float(psi_score),
                'psi_drift': psi_drift,
                'overall_drift': ks_drift or psi_drift,
                'current_mean': float(np.mean(current_data)),
                'baseline_mean': baseline_stats['mean'],
                'current_std': float(np.std(current_data)),
                'baseline_std': baseline_stats['std']
            }
            
            drift_results['feature_results'][feature] = feature_result
            
            if not np.isnan(ks_pvalue):
                ks_pvalues.append(ks_pvalue)
            if not np.isinf(psi_score):
                psi_scores.append(psi_score)
            
            if feature_result['overall_drift']:
                drift_results['overall_drift_detected'] = True
            
            if verbose:
                logger.info(f"特征 {feature}: KS p-value={ks_pvalue:.4f}, PSI={psi_score:.4f}, 漂移={'是' if feature_result['overall_drift'] else '否'}")
        
        # 计算汇总统计
        drift_results['summary'] = {
            'total_features': len(drift_results['feature_results']),
            'drifted_features_ks': sum(1 for r in drift_results['feature_results'].values() if r['ks_drift']),
            'drifted_features_psi': sum(1 for r in drift_results['feature_results'].values() if r['psi_drift']),
            'avg_ks_pvalue': np.mean(ks_pvalues) if ks_pvalues else 1.0,
            'avg_psi_score': np.mean(psi_scores) if psi_scores else 0.0
        }
        
        # 保存检测历史
        self.drift_history.append(drift_results)
        
        if verbose:
            summary = drift_results['summary']
            logger.info(f"[DataDriftDetector] 漂移检测完成: {summary['drifted_features_ks']}/{summary['total_features']} 特征KS漂移, {summary['drifted_features_psi']}/{summary['total_features']} 特征PSI漂移")
        
        return drift_results


class PerformanceDegradationDetector:
    """性能衰退检测器"""

    def __init__(self, breakeven_threshold: float = 0.54,
                 consecutive_periods: int = 3, window_size: int = 100):
        """
        初始化性能衰退检测器

        Args:
            breakeven_threshold: 盈亏平衡胜率阈值
            consecutive_periods: 连续低于阈值的周期数
            window_size: 滑动窗口大小
        """
        self.breakeven_threshold = breakeven_threshold
        self.consecutive_periods = consecutive_periods
        self.window_size = window_size

        # 性能监控历史
        self.performance_history: List[Dict] = []
        self.consecutive_poor_count = 0

        logger.info(f"[PerformanceDegradationDetector] 初始化完成，阈值={breakeven_threshold}, 连续周期={consecutive_periods}")

    def check_performance_degradation(self, target_name: str = "meta_model") -> Dict[str, Any]:
        """
        检查性能衰退

        Args:
            target_name: 目标模型名称

        Returns:
            性能检查结果
        """
        try:
            from .dynamic_win_rate_tracker import get_dynamic_win_rate_tracker

            win_rate_tracker = get_dynamic_win_rate_tracker()

            # 获取当前胜率
            current_win_rate, settled_trades = win_rate_tracker.get_dynamic_win_rate(
                target_name=target_name,
                min_trades=10,
                default_rate=0.5
            )

            # 获取胜率趋势
            win_rate_trend = win_rate_tracker.get_win_rate_trend(target_name, periods=5)

            # 检查是否低于阈值
            is_below_threshold = current_win_rate < self.breakeven_threshold

            if is_below_threshold:
                self.consecutive_poor_count += 1
            else:
                self.consecutive_poor_count = 0

            # 判断是否需要重训练
            needs_retrain = (
                self.consecutive_poor_count >= self.consecutive_periods and
                settled_trades >= 10  # 确保有足够的样本
            )

            # 计算趋势斜率（如果有足够的趋势数据）
            trend_slope = 0.0
            if len(win_rate_trend) >= 3:
                x = np.arange(len(win_rate_trend))
                trend_slope = np.polyfit(x, win_rate_trend, 1)[0]

            result = {
                'timestamp': datetime.now().isoformat(),
                'target_name': target_name,
                'current_win_rate': current_win_rate,
                'settled_trades': settled_trades,
                'breakeven_threshold': self.breakeven_threshold,
                'is_below_threshold': is_below_threshold,
                'consecutive_poor_count': self.consecutive_poor_count,
                'consecutive_threshold': self.consecutive_periods,
                'needs_retrain': needs_retrain,
                'win_rate_trend': win_rate_trend,
                'trend_slope': trend_slope,
                'trend_direction': 'declining' if trend_slope < -0.01 else 'stable' if abs(trend_slope) <= 0.01 else 'improving'
            }

            # 记录历史
            self.performance_history.append(result)

            return result

        except Exception as e:
            logger.error(f"[PerformanceDegradationDetector] 检查性能衰退失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'target_name': target_name,
                'error': str(e),
                'needs_retrain': False
            }


class ModelImmunitySystem:
    """模型免疫系统 - 统一的性能监控和自动重训练触发器"""

    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化模型免疫系统

        Args:
            config: 免疫系统配置
        """
        self.config = config or self._get_default_config()

        # 初始化子系统
        self.drift_detector = DataDriftDetector(
            key_features=self.config.get('key_features'),
            ks_threshold=self.config.get('ks_threshold', 0.05),
            psi_threshold=self.config.get('psi_threshold', 0.2)
        )

        self.performance_detector = PerformanceDegradationDetector(
            breakeven_threshold=self.config.get('breakeven_threshold', 0.54),
            consecutive_periods=self.config.get('consecutive_periods', 3),
            window_size=self.config.get('window_size', 100)
        )

        # 免疫系统状态
        self.immunity_history: List[Dict] = []
        self.last_retrain_trigger = None
        self.retrain_cooldown_hours = self.config.get('retrain_cooldown_hours', 6)

        # 线程锁
        self.lock = threading.RLock()

        logger.info("[ModelImmunitySystem] 免疫系统初始化完成")

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'key_features': [
                'global_atr_percent', 'meta_prob_diff_up_vs_down', 'global_trend_strength',
                'global_adx', 'global_ema_short', 'global_ema_long', 'volume_x_price_change',
                'rsi_velocity', 'bb_width_x_volume_anomaly'
            ],
            'ks_threshold': 0.05,
            'psi_threshold': 0.2,
            'breakeven_threshold': 0.54,
            'consecutive_periods': 3,
            'window_size': 100,
            'retrain_cooldown_hours': 6,
            'enable_drift_detection': True,
            'enable_performance_monitoring': True,
            'drift_weight': 0.3,
            'performance_weight': 0.7
        }

    def update_baseline_distributions(self, training_data: pd.DataFrame):
        """更新基准分布"""
        if self.config.get('enable_drift_detection', True):
            self.drift_detector.save_baseline_distributions(training_data)

    def update_realtime_data(self, new_data: pd.DataFrame):
        """更新实时数据"""
        if self.config.get('enable_drift_detection', True):
            self.drift_detector.update_realtime_data(new_data)

    def comprehensive_health_check(self, target_name: str = "meta_model",
                                 verbose: bool = False) -> Dict[str, Any]:
        """
        综合健康检查

        Args:
            target_name: 目标模型名称
            verbose: 是否输出详细信息

        Returns:
            综合检查结果
        """
        with self.lock:
            health_check_result = {
                'timestamp': datetime.now().isoformat(),
                'target_name': target_name,
                'overall_health': 'healthy',
                'needs_retrain': False,
                'trigger_reasons': [],
                'drift_detection': {},
                'performance_monitoring': {},
                'recommendation': 'continue_monitoring'
            }

            # 1. 数据漂移检测
            if self.config.get('enable_drift_detection', True):
                try:
                    drift_result = self.drift_detector.detect_drift(verbose=verbose)
                    health_check_result['drift_detection'] = drift_result

                    if drift_result.get('overall_drift_detected', False):
                        health_check_result['trigger_reasons'].append('data_drift')
                        if verbose:
                            logger.warning(f"[ModelImmunitySystem] 检测到数据漂移: {target_name}")

                except Exception as e:
                    logger.error(f"[ModelImmunitySystem] 数据漂移检测失败: {e}")
                    health_check_result['drift_detection'] = {'error': str(e)}

            # 2. 性能衰退检测
            if self.config.get('enable_performance_monitoring', True):
                try:
                    performance_result = self.performance_detector.check_performance_degradation(target_name)
                    health_check_result['performance_monitoring'] = performance_result

                    if performance_result.get('needs_retrain', False):
                        health_check_result['trigger_reasons'].append('performance_degradation')
                        if verbose:
                            logger.warning(f"[ModelImmunitySystem] 检测到性能衰退: {target_name}")

                except Exception as e:
                    logger.error(f"[ModelImmunitySystem] 性能监控失败: {e}")
                    health_check_result['performance_monitoring'] = {'error': str(e)}

            # 3. 综合判断
            if health_check_result['trigger_reasons']:
                # 检查冷却期
                if self._is_in_cooldown():
                    health_check_result['needs_retrain'] = False
                    health_check_result['recommendation'] = 'wait_cooldown'
                    health_check_result['overall_health'] = 'degraded_but_cooling'
                    if verbose:
                        logger.info(f"[ModelImmunitySystem] 在冷却期内，暂不触发重训练")
                else:
                    health_check_result['needs_retrain'] = True
                    health_check_result['recommendation'] = 'immediate_retrain'
                    health_check_result['overall_health'] = 'critical'

                    # 记录触发时间
                    self.last_retrain_trigger = datetime.now()

                    if verbose:
                        logger.critical(f"[ModelImmunitySystem] 触发重训练: {target_name}, 原因: {health_check_result['trigger_reasons']}")

            # 记录历史
            self.immunity_history.append(health_check_result)

            return health_check_result

    def _is_in_cooldown(self) -> bool:
        """检查是否在冷却期内"""
        if self.last_retrain_trigger is None:
            return False

        time_since_last = datetime.now() - self.last_retrain_trigger
        return time_since_last.total_seconds() < self.retrain_cooldown_hours * 3600

    def get_immunity_summary(self) -> Dict[str, Any]:
        """获取免疫系统摘要"""
        if not self.immunity_history:
            return {'status': 'no_data'}

        recent_checks = self.immunity_history[-10:]  # 最近10次检查

        return {
            'total_checks': len(self.immunity_history),
            'recent_checks': len(recent_checks),
            'retrain_triggers': sum(1 for check in recent_checks if check.get('needs_retrain', False)),
            'drift_detections': sum(1 for check in recent_checks if check.get('drift_detection', {}).get('overall_drift_detected', False)),
            'performance_issues': sum(1 for check in recent_checks if check.get('performance_monitoring', {}).get('needs_retrain', False)),
            'last_check': recent_checks[-1]['timestamp'] if recent_checks else None,
            'last_retrain_trigger': self.last_retrain_trigger.isoformat() if self.last_retrain_trigger else None,
            'in_cooldown': self._is_in_cooldown()
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动重训练调度器

此模块实现了模型的自动化滚动窗口重训练功能，包括：
1. 定时触发重训练
2. 滚动窗口数据管理
3. 模型性能监控和自动重训练决策
4. 训练历史记录和日志管理
"""

import os
import json
import logging # logging 模块通常用于更灵活的日志记录，但当前脚本主要用print
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import shutil

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
import pandas as pd
import numpy as np

# 导入配置
try:
    import auto_retrain_config # 直接导入，后续使用 auto_retrain_config.XXX
except ImportError:
    print("Warning: auto_retrain_config.py not found, using default settings for auto_retrain_config related params")
    auto_retrain_config = None # 设置为 None，后续代码需要处理这种情况

# 导入现有模块
import main
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.core import data_utils # 假设 data_utils 是项目的一部分，如果不是，需要处理
from src.utils.dynamic_config_manager import DynamicConfigManager # 假设在同一路径或可导入
import pytz
import traceback

try:
    import config as main_project_config # Main project config
except ImportError:
    print("CRITICAL ERROR: The main 'config.py' file is missing for AutoRetrainScheduler.")
    main_project_config = None


class AutoRetrainScheduler:
    """
    自动化滚动窗口重训练调度器
    """
    
    def __init__(self, config_manager: Optional[DynamicConfigManager] = None):
        if config_manager is None:
            # 使用 auto_retrain_config.py 中定义的 WORK_DIR (如果可用)
            work_dir_base = Path(getattr(auto_retrain_config, 'WORK_DIR', "."))
            config_filepath = work_dir_base / 'config' / 'auto_retrain_config.json'
            config_filepath.parent.mkdir(parents=True, exist_ok=True) # 确保目录存在
            self.config_manager = DynamicConfigManager(str(config_filepath)) # DCM期望字符串路径
        else:
            self.config_manager = config_manager
            
        self.scheduler = None
        self.is_running = False
        self.training_lock = threading.Lock()
        
        self.training_history = {}
        self.performance_metrics = {}
        self.last_training_times = {}
        
        self.retrain_config = self._load_retrain_config()
        
        # 从 auto_retrain_config.py 加载参数，如果文件存在且导入成功
        if auto_retrain_config:
            self.retrain_interval_hours = getattr(auto_retrain_config, 'RETRAIN_INTERVAL_HOURS', 24)
            self.performance_threshold = getattr(auto_retrain_config, 'PERFORMANCE_THRESHOLD', 0.55)
            self.min_samples = getattr(auto_retrain_config, 'MIN_TRAINING_SAMPLES', 1000)
            self.max_model_history = getattr(auto_retrain_config, 'MAX_MODEL_HISTORY', 10)
            self.min_retrain_interval_hours = getattr(auto_retrain_config, 'MIN_RETRAIN_INTERVAL_HOURS', 6)
            self.consecutive_poor_threshold = getattr(auto_retrain_config, 'CONSECUTIVE_POOR_PERFORMANCE_THRESHOLD', 3)
            self.performance_window_size = getattr(auto_retrain_config, 'PERFORMANCE_WINDOW_SIZE', 100)
            self.training_window_days = getattr(auto_retrain_config, 'TRAINING_WINDOW_DAYS', 30)
            self.validation_window_days = getattr(auto_retrain_config, 'VALIDATION_WINDOW_DAYS', 7)
            self.enable_notifications = getattr(auto_retrain_config, 'ENABLE_NOTIFICATIONS', True)
            self.enable_model_comparison = getattr(auto_retrain_config, 'ENABLE_MODEL_COMPARISON', True)
            self.model_improvement_threshold = getattr(auto_retrain_config, 'MODEL_IMPROVEMENT_THRESHOLD', 0.02)
            self.work_dir = Path(getattr(auto_retrain_config, 'WORK_DIR', "."))
            self.model_backup_dir = self.work_dir / getattr(auto_retrain_config, 'MODEL_BACKUP_DIR', "model_backups")
            self.apscheduler_timezone_str = getattr(auto_retrain_config, 'APScheduler_TIMEZONE', 'Asia/Shanghai') # 使用 auto_retrain_config 的时区
            self.cleanup_cron_expression = getattr(auto_retrain_config, 'CLEANUP_CRON', '0 3 * * 0')
            self.data_retention_days_val = getattr(auto_retrain_config, 'DATA_RETENTION_DAYS', 90)

        else: # auto_retrain_config.py 未能导入时的硬编码默认值
            self.retrain_interval_hours = 24
            self.performance_threshold = 0.55
            self.min_samples = 1000
            self.max_model_history = 10
            self.min_retrain_interval_hours = 6
            self.consecutive_poor_threshold = 3
            self.performance_window_size = 100
            self.training_window_days = 30
            self.validation_window_days = 7
            self.enable_notifications = True
            self.enable_model_comparison = True
            self.model_improvement_threshold = 0.02
            self.work_dir = Path(".")
            self.model_backup_dir = self.work_dir / "model_backups"
            self.apscheduler_timezone_str = 'Asia/Shanghai'
            self.cleanup_cron_expression = '0 3 * * 0'
            self.data_retention_days_val = 90

        self.log_dir = self.work_dir / "logs" / "auto_retrain" # 将日志也放在工作目录下
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.model_backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.performance_thresholds = {
            'accuracy_drop_threshold': 1.0 - self.performance_threshold,
            'days_since_last_train': self.retrain_interval_hours / 24,
            'min_samples_for_retrain': self.min_samples,
            'performance_window_days': self.training_window_days
        }
        
        self.consecutive_poor_count = 0
        self.last_retrain_time = None # 这个应该是字典，针对每个target
        self.last_training_times = {} # 改为实例变量
        
        print("[AutoRetrain] 自动重训练调度器已初始化")
    
    def _load_retrain_config(self) -> Dict[str, Any]:
        """加载JSON重训练配置 (auto_retrain_config.json)"""
        default_config = {
            'enabled': True, 'schedule_type': 'cron', 'cron_expression': '0 2 * * *',
            'interval_hours': 24, 'rolling_window_days': 90,
            'min_training_interval_hours': 6, 'performance_monitoring': True,
            'auto_retrain_on_performance_drop': True, 'backup_old_models': True,
            'max_backup_models': 5,
            'cleanup_cron': '0 3 * * 0' # 添加清理任务的cron配置
        }
        
        # 使用与 __init__ 中相同的逻辑确定 config_filepath
        work_dir_base = Path(getattr(auto_retrain_config, 'WORK_DIR', ".")) if auto_retrain_config else Path(".")
        config_file = work_dir_base / 'config' / 'auto_retrain_config.json'
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                print(f"[AutoRetrain] 已加载JSON配置文件: {config_file}")
            except Exception as e:
                print(f"[AutoRetrain] JSON配置文件加载失败: {e}，使用内部默认配置。")
        else:
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            print(f"[AutoRetrain] 已创建默认JSON配置文件: {config_file}")
        
        return default_config
    
    def start(self):
        if not self.retrain_config.get('enabled', True):
            print("[AutoRetrain] 自动重训练功能已在JSON配置中禁用")
            return
        
        if self.is_running:
            print("[AutoRetrain] 调度器已在运行中")
            return
        
        if not main_project_config: # 检查主项目配置是否加载成功
            print("[AutoRetrain] 严重错误: 主项目config.py未加载，无法启动调度器。")
            return

        try:
            timezone_str = self.apscheduler_timezone_str # 使用从auto_retrain_config.py或默认值获取的时区
            try:
                tz = pytz.timezone(timezone_str)
            except Exception as e_tz:
                tz = pytz.UTC
                print(f"[AutoRetrain] 时区 '{timezone_str}' 无效 ({e_tz})，使用UTC。")
            
            self.scheduler = BackgroundScheduler(timezone=tz)
            self._add_retrain_jobs()
            if self.retrain_config.get('performance_monitoring', True):
                self._add_performance_monitoring_job()
            self._add_cleanup_job() # 添加清理任务
            
            self.scheduler.start()
            self.is_running = True
            print("[AutoRetrain] 自动重训练调度器已启动")
            self._log_scheduler_status()
        except Exception as e:
            print(f"[AutoRetrain] 启动调度器失败: {e}")
            traceback.print_exc()
    
    def stop(self):
        if self.scheduler and self.is_running:
            try:
                self.scheduler.shutdown(wait=False)
                self.is_running = False
                print("[AutoRetrain] 自动重训练调度器已停止")
            except Exception as e:
                print(f"[AutoRetrain] 停止调度器失败: {e}")
    
    def _add_retrain_jobs(self):
        schedule_type = self.retrain_config.get('schedule_type', 'cron')
        trigger = None # 初始化trigger

        if schedule_type == 'cron':
            cron_expr = self.retrain_config.get('cron_expression', '0 2 * * *')
            cron_parts = cron_expr.split()
            if len(cron_parts) == 5:
                minute, hour, day, month, day_of_week = cron_parts
                trigger = CronTrigger(minute=minute, hour=hour, day=day, month=month, day_of_week=day_of_week)
            else:
                trigger = CronTrigger(hour=2, minute=0)
                print(f"[AutoRetrain] Cron表达式 '{cron_expr}' 格式错误，使用默认时间 02:00")
        elif schedule_type == 'interval':
            interval_hours = self.retrain_config.get('interval_hours', 24)
            trigger = IntervalTrigger(hours=interval_hours)
        else:
            print(f"[AutoRetrain] 未知的调度类型: {schedule_type}，无法添加重训练作业。")
            return

        if not main_project_config or not hasattr(main_project_config, 'PREDICTION_TARGETS'):
            print("[AutoRetrain] main_project_config.PREDICTION_TARGETS 未找到，无法添加重训练作业。")
            return
        
        for target_info_dict in main_project_config.PREDICTION_TARGETS:
            if isinstance(target_info_dict, dict) and 'name' in target_info_dict:
                target_name = target_info_dict['name']
                try:
                    # 使用 main_project_config 来获取目标配置
                    target_config_detail = main_project_config.get_target_config(target_name)
                    if not target_config_detail:
                        print(f"[AutoRetrain] 未能获取目标 '{target_name}' 的详细配置，跳过添加作业。")
                        continue
                    if target_config_detail.get('target_variable_type') == 'META_MODEL_DISPLAY':
                        print(f"[AutoRetrain] 跳过为元模型显示目标 '{target_name}' 添加重训练作业。")
                        continue
                    
                    job_id = f"retrain_{target_name.replace(' ', '_')}" # 确保job_id有效
                    self.scheduler.add_job(
                        func=self._retrain_target_job, trigger=trigger, args=[target_name],
                        id=job_id, name=f"重训练 {target_name}", max_instances=1, coalesce=True
                    )
                    print(f"[AutoRetrain] 已添加重训练任务: {target_name} (ID: {job_id})")
                except Exception as e_add:
                    print(f"[AutoRetrain] 为目标 '{target_name}' 添加重训练作业失败: {e_add}")
                    traceback.print_exc(limit=1)

    def _add_performance_monitoring_job(self):
        # 使用 auto_retrain_config.py (别名是 config) 中的 cron 表达式
        cron_expr = getattr(auto_retrain_config, 'PERFORMANCE_CHECK_CRON', '0 * * * *') if auto_retrain_config else '0 * * * *'
        cron_parts = cron_expr.split()
        if len(cron_parts) == 5:
            minute, hour, day, month, day_of_week = cron_parts
            trigger = CronTrigger(minute=minute, hour=hour, day=day, month=month, day_of_week=day_of_week)
        else:
            trigger = IntervalTrigger(hours=1) # Fallback
            print(f"[AutoRetrain] 性能监控Cron表达式 '{cron_expr}' 格式错误，使用每小时触发。")
        
        self.scheduler.add_job(
            func=self._performance_monitoring_job, trigger=trigger,
            id="performance_monitoring", name="性能监控", max_instances=1
        )
        print(f"[AutoRetrain] 已添加性能监控任务，触发器: {trigger}")

    def _add_cleanup_job(self):
        cron_expr = self.cleanup_cron_expression # 使用从 __init__ 加载的属性
        cron_parts = cron_expr.split()
        if len(cron_parts) == 5:
            minute, hour, day, month, day_of_week = cron_parts
            trigger = CronTrigger(minute=minute, hour=hour, day=day, month=month, day_of_week=day_of_week)
        else:
            trigger = CronTrigger(hour=3, minute=0, day_of_week=0) # Fallback
            print(f"[AutoRetrain] 清理任务Cron表达式 '{cron_expr}' 格式错误，使用默认。")
        
        self.scheduler.add_job(
            func=self._cleanup_job, trigger=trigger,
            id="data_cleanup", name="数据清理", max_instances=1
        )
        print(f"[AutoRetrain] 已添加数据清理任务，触发器: {trigger}")

    def _retrain_target_job(self, target_name: str):
        try:
            print(f"[AutoRetrain] 触发重训练任务: {target_name} at {datetime.now(self.scheduler.timezone if self.scheduler else pytz.UTC)}")
            if not self._should_retrain(target_name):
                print(f"[AutoRetrain] {target_name} 不满足重训练条件或正在训练，跳过此次计划。")
                return
            
            success = self._execute_retrain(target_name)
            self._record_training_result(target_name, success)
            print(f"[AutoRetrain] {target_name} 重训练任务完成，结果: {'成功' if success else '失败'}")
        except Exception as e:
            print(f"[AutoRetrain] 重训练任务执行失败 ({target_name}): {e}")
            traceback.print_exc()
            self._record_training_result(target_name, False, str(e))
    
    def _performance_monitoring_job(self):
        try:
            print(f"[AutoRetrain] 执行性能监控检查 at {datetime.now(self.scheduler.timezone if self.scheduler else pytz.UTC)}")
            if not main_project_config or not hasattr(main_project_config, 'PREDICTION_TARGETS'):
                print("[AutoRetrain PMon] main_project_config.PREDICTION_TARGETS 未找到。")
                return

            for target_info_dict in main_project_config.PREDICTION_TARGETS:
                if isinstance(target_info_dict, dict) and 'name' in target_info_dict:
                    target_name = target_info_dict['name']
                    try:
                        target_config_detail = main_project_config.get_target_config(target_name)
                        if not target_config_detail: continue
                        if target_config_detail.get('target_variable_type') == 'META_MODEL_DISPLAY': continue
                        
                        if self._check_performance_degradation(target_name): # 检查性能是否下降
                            print(f"[AutoRetrain PMon] 检测到 {target_name} 性能可能下降，触发按需重训练。")
                            self._retrain_target_job(target_name) # 如果性能下降，则调用重训练
                    except Exception as e_target_pm:
                         print(f"[AutoRetrain PMon] 处理目标 '{target_name}' 性能监控时出错: {e_target_pm}")
                         traceback.print_exc(limit=1)
        except Exception as e:
            print(f"[AutoRetrain PMon] 性能监控任务执行失败: {e}")
            traceback.print_exc()

    def _should_retrain(self, target_name: str) -> bool:
        try:
            min_interval_hours = self.min_retrain_interval_hours # 使用实例属性
            last_train_time = self.last_training_times.get(target_name)
            
            if last_train_time:
                time_since_last = datetime.now(pytz.UTC if last_train_time.tzinfo is None else last_train_time.tzinfo) - last_train_time
                if time_since_last.total_seconds() < min_interval_hours * 3600:
                    print(f"[AutoRetrain Should] {target_name} 距离上次训练 ({time_since_last}) 过短 (小于 {min_interval_hours}h)，跳过。")
                    return False
            
            if main.training_in_progress_flag.is_set(): # 检查主程序的全局训练标志
                print(f"[AutoRetrain Should] 系统当前已有其他训练任务进行中，跳过 {target_name}。")
                return False
            
            if not self._check_data_availability(target_name): # 检查数据可用性
                print(f"[AutoRetrain Should] {target_name} 数据不足，跳过重训练。")
                return False
            
            print(f"[AutoRetrain Should] {target_name} 满足重训练基本条件。")
            return True
        except Exception as e:
            print(f"[AutoRetrain Should] 检查重训练条件失败 ({target_name}): {e}")
            traceback.print_exc(limit=1)
            return False
    
    def _check_data_availability(self, target_name: str) -> bool:
        """检查特定目标的数据可用性"""
        try:
            if not main_project_config: return False # 主配置未加载
            target_config_detail = main_project_config.get_target_config(target_name)
            if not target_config_detail: return False

            symbol = target_config_detail.get('symbol', getattr(main_project_config, 'SYMBOL', 'BTCUSDT'))
            interval = target_config_detail.get('interval', '15m')
            
            rolling_days = self.training_window_days # 使用实例属性
            min_samples_req = self.min_samples         # 使用实例属性
            
            # 此处应有实际逻辑通过 data_utils 或直接调用 API 检查数据量
            # 简化示例：假设如果能获取到最近少量数据就认为数据服务可用
            client = main.binance_client # 使用 main.py 中的 Binance 客户端
            if not client:
                print(f"[AutoRetrain DataCheck - {target_name}] Binance client 未初始化。")
                return False # 如果没有客户端，无法检查数据
            
            # 尝试获取少量近期数据作为可用性检查
            df_check = data_utils.fetch_binance_history(client, symbol, interval, limit=min_samples_req // 20 or 50)
            if df_check is not None and len(df_check) >= (min_samples_req // 20 or 50): # 检查获取到的数据是否达到一个最小阈值
                print(f"[AutoRetrain DataCheck - {target_name}] 数据服务可用，获取到 {len(df_check)} 条近期K线。")
                return True
            else:
                print(f"[AutoRetrain DataCheck - {target_name}] 数据获取失败或数据量不足 ({len(df_check) if df_check is not None else 0} 条)。")
                return False
        except Exception as e:
            print(f"[AutoRetrain DataCheck - {target_name}] 检查数据可用性时出错: {e}")
            traceback.print_exc(limit=1)
            return False

    def _check_performance_degradation(self, target_name: str) -> bool:
        """🎯 增强的性能衰退检查 - 集成免疫系统"""
        try:
            # 导入免疫系统
            from src.core.model_immunity_system import ModelImmunitySystem

            # 创建或获取免疫系统实例
            if not hasattr(self, '_immunity_system'):
                immunity_config = {
                    'breakeven_threshold': getattr(auto_retrain_config, 'PERFORMANCE_THRESHOLD', 0.54),
                    'consecutive_periods': getattr(auto_retrain_config, 'CONSECUTIVE_POOR_PERFORMANCE_THRESHOLD', 3),
                    'retrain_cooldown_hours': self.min_retrain_interval_hours,
                    'enable_drift_detection': True,
                    'enable_performance_monitoring': True
                }
                self._immunity_system = ModelImmunitySystem(immunity_config)
                print(f"[AutoRetrain] 为 {target_name} 初始化免疫系统")

            # 执行综合健康检查
            health_check = self._immunity_system.comprehensive_health_check(
                target_name=target_name,
                verbose=True
            )

            # 记录检查结果
            needs_retrain = health_check.get('needs_retrain', False)
            trigger_reasons = health_check.get('trigger_reasons', [])
            overall_health = health_check.get('overall_health', 'unknown')

            if needs_retrain:
                print(f"[AutoRetrain ImmunityCheck - {target_name}] 🚨 免疫系统触发重训练")
                print(f"  健康状态: {overall_health}")
                print(f"  触发原因: {', '.join(trigger_reasons)}")

                # 详细信息
                if 'performance_degradation' in trigger_reasons:
                    perf_info = health_check.get('performance_monitoring', {})
                    current_win_rate = perf_info.get('current_win_rate', 0)
                    consecutive_count = perf_info.get('consecutive_poor_count', 0)
                    print(f"  性能衰退: 胜率={current_win_rate:.3f}, 连续低性能={consecutive_count}次")

                if 'data_drift' in trigger_reasons:
                    drift_info = health_check.get('drift_detection', {})
                    summary = drift_info.get('summary', {})
                    drifted_ks = summary.get('drifted_features_ks', 0)
                    drifted_psi = summary.get('drifted_features_psi', 0)
                    total_features = summary.get('total_features', 0)
                    print(f"  数据漂移: KS漂移={drifted_ks}/{total_features}, PSI漂移={drifted_psi}/{total_features}")

                return True
            else:
                if overall_health == 'degraded_but_cooling':
                    print(f"[AutoRetrain ImmunityCheck - {target_name}] ⏳ 检测到问题但在冷却期内")
                else:
                    print(f"[AutoRetrain ImmunityCheck - {target_name}] ✅ 免疫系统检查通过，健康状态: {overall_health}")
                return False

        except ImportError:
            print(f"[AutoRetrain PerfDeg - {target_name}] ⚠️ 免疫系统不可用，使用传统检查方法")
            return self._legacy_performance_check(target_name)
        except Exception as e:
            print(f"[AutoRetrain PerfDeg - {target_name}] ❌ 免疫系统检查失败: {e}")
            return self._legacy_performance_check(target_name)

    def _legacy_performance_check(self, target_name: str) -> bool:
        """传统的性能检查方法（作为备用）"""
        # --- 1. 基于时间的强制重训练检查 ---
        last_train_dt = self.last_training_times.get(target_name)
        if last_train_dt:
            time_since_last_train = datetime.now(pytz.UTC if last_train_dt.tzinfo is None else last_train_dt.tzinfo) - last_train_dt
            if time_since_last_train.total_seconds() >= self.retrain_interval_hours * 3600:
                print(f"[AutoRetrain Legacy - {target_name}] 距离上次训练已超过 {self.retrain_interval_hours} 小时，建议重训练。")
                return True
        else:
            print(f"[AutoRetrain Legacy - {target_name}] 模型从未训练过，建议重训练。")
            return True

        # --- 2. 基于简单阈值的性能检查 (此处为示例，实际应更复杂) ---
        # 假设 self.performance_metrics[target_name] 存储了类似 [{'timestamp': ..., 'accuracy': ...}, ...] 的列表
        metrics_history = self.performance_metrics.get(target_name, [])
        if len(metrics_history) < self.performance_window_size: # 需要足够多的样本来评估性能
            # print(f"[AutoRetrain PerfDeg - {target_name}] 性能指标样本不足 ({len(metrics_history)} < {self.performance_window_size})，暂不评估。")
            return False # 指标不足，暂时不判断为性能下降

        # 简单示例：检查最近 N 次的平均准确率是否低于阈值
        recent_accuracies = [m.get(getattr(auto_retrain_config, 'PRIMARY_METRIC', 'accuracy'), 0.0)
                             for m in metrics_history[-self.performance_window_size:]
                             if isinstance(m, dict) and isinstance(m.get(getattr(auto_retrain_config, 'PRIMARY_METRIC', 'accuracy')), (int, float))]
        
        if not recent_accuracies: # 如果没有有效的准确率数据
             # print(f"[AutoRetrain PerfDeg - {target_name}] 最近性能窗口内无有效准确率数据。")
             return False

        avg_recent_accuracy = np.mean(recent_accuracies) if recent_accuracies else 0.0
        
        # 使用 self.performance_threshold (来自 auto_retrain_config.py)
        if avg_recent_accuracy < self.performance_threshold:
            self.consecutive_poor_count += 1
            print(f"[AutoRetrain PerfDeg - {target_name}] 最近平均准确率 {avg_recent_accuracy:.3f} 低于阈值 {self.performance_threshold:.3f}。连续低性能次数: {self.consecutive_poor_count}")
            if self.consecutive_poor_count >= self.consecutive_poor_threshold: # 使用实例属性
                print(f"[AutoRetrain PerfDeg - {target_name}] 已连续 {self.consecutive_poor_count} 次性能不佳，触发重训练。")
                self.consecutive_poor_count = 0 # 重置计数器
                return True
        else:
            self.consecutive_poor_count = 0 # 性能达标，重置计数器
            # print(f"[AutoRetrain PerfDeg - {target_name}] 最近平均准确率 {avg_recent_accuracy:.3f} 正常。")

        return False # 默认不认为性能下降

    def _execute_retrain(self, target_name: str) -> bool:
        try:
            with self.training_lock: # 确保同一时间只有一个重训练任务执行
                print(f"[AutoRetrain Execute] 开始执行 {target_name} 的重训练流程。")
                if main.training_in_progress_flag.is_set():
                    print(f"[AutoRetrain Execute] 主程序已标记训练进行中，跳过 {target_name}。")
                    return False # 避免并发训练

                # 1. 备份现有模型 (如果启用)
                if self.enable_model_comparison or getattr(auto_retrain_config, 'backup_old_models', True): # 考虑两个开关
                    self._backup_existing_models(target_name)

                main.training_in_progress_flag.set() # 设置主程序的训练标志
                
                # 2. 准备训练参数和调用主训练流程
                # target_type_filter 需要根据 target_name 的配置来确定
                target_config_detail = main_project_config.get_target_config(target_name)
                if not target_config_detail:
                    print(f"[AutoRetrain Execute] 获取目标 '{target_name}' 配置失败，无法执行重训练。")
                    main.training_in_progress_flag.clear()
                    return False
                
                target_type_from_cfg = target_config_detail.get('target_variable_type', 'BOTH').upper()
                filter_for_training = None
                if target_type_from_cfg == "UP_ONLY": filter_for_training = "UP_ONLY"
                elif target_type_from_cfg == "DOWN_ONLY": filter_for_training = "DOWN_ONLY"
                # 对于 "BOTH" 或其他未明确指定的情况，filter_for_training 为 None，会训练所有（或匹配该名称的）
                
                print(f"[AutoRetrain Execute] 调用 main.run_training_pipeline for {target_name} with filter: {filter_for_training}")

                # 🎯 重训练前：更新免疫系统的基准分布
                try:
                    if hasattr(self, '_immunity_system'):
                        print(f"[AutoRetrain Execute] 重训练完成后将更新免疫系统基准分布")
                except Exception as e_immunity_prep:
                    print(f"[AutoRetrain Execute] 免疫系统准备失败: {e_immunity_prep}")

                # 调用 main.py 中的训练流程
                main.run_training_pipeline(target_type_filter=filter_for_training)

                # 🎯 重训练后：更新免疫系统的基准分布
                try:
                    if hasattr(self, '_immunity_system'):
                        # 这里需要获取新训练的数据来更新基准分布
                        # 实际实现中，可能需要从训练流程中获取训练数据
                        print(f"[AutoRetrain Execute] 重训练完成，免疫系统基准分布将在下次数据更新时刷新")
                except Exception as e_immunity_update:
                    print(f"[AutoRetrain Execute] 免疫系统基准分布更新失败: {e_immunity_update}")

                # 3. (可选) 新旧模型比较 (如果启用)
                if self.enable_model_comparison: # 使用实例属性
                    # 此处需要实现比较逻辑：加载新训练的模型和备份的旧模型，在验证集上评估并比较
                    # 如果新模型性能未达到 self.model_improvement_threshold，则可能回滚到旧模型
                    print(f"[AutoRetrain Execute - {target_name}] 模型比较逻辑未完全实现。")
                    pass # Placeholder

                self.last_training_times[target_name] = datetime.now(pytz.UTC) # 记录带时区的训练时间
                print(f"[AutoRetrain Execute] {target_name} 重训练调用完成。")
                return True
        except Exception as e:
            print(f"[AutoRetrain Execute] 执行重训练 ({target_name}) 时发生严重错误: {e}")
            traceback.print_exc()
            return False
        finally:
            main.training_in_progress_flag.clear() # 确保在任何情况下都清除标志

    def _backup_existing_models(self, target_name: str):
        try:
            if not main_project_config: return
            target_config_detail = main_project_config.get_target_config(target_name)
            if not target_config_detail: return

            model_dir_str = target_config_detail.get('model_save_dir')
            if not model_dir_str:
                print(f"[AutoRetrain Backup - {target_name}] 未配置 model_save_dir，无法备份。")
                return
            
            model_dir_path = Path(model_dir_str)
            if not model_dir_path.exists() or not model_dir_path.is_dir():
                print(f"[AutoRetrain Backup - {target_name}] 模型目录 '{model_dir_str}' 不存在或不是目录。")
                return

            backup_root_dir = self.model_backup_dir # 使用实例属性
            target_backup_dir = backup_root_dir / model_dir_path.name # 为每个目标创建一个子备份目录
            target_backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            # 备份整个模型目录
            backup_name = f"{model_dir_path.name}_backup_{timestamp_str}"
            full_backup_path = target_backup_dir / backup_name
            
            if model_dir_path.exists():
                shutil.copytree(model_dir_path, full_backup_path, dirs_exist_ok=True)
                print(f"[AutoRetrain Backup - {target_name}] 模型已备份到: {full_backup_path}")
            
            self._cleanup_old_backups(target_backup_dir) # 清理此目标的旧备份
        except Exception as e:
            print(f"[AutoRetrain Backup - {target_name}] 备份模型时出错: {e}")
            traceback.print_exc(limit=1)

    def _cleanup_old_backups(self, specific_target_backup_dir: Path):
        try:
            max_hist = self.max_model_history # 使用实例属性
            
            backup_sub_dirs = [d for d in specific_target_backup_dir.iterdir() if d.is_dir()]
            # 按修改时间排序，最新的在前面
            backup_sub_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            if len(backup_sub_dirs) > max_hist:
                for dir_to_delete in backup_sub_dirs[max_hist:]:
                    shutil.rmtree(dir_to_delete)
                    print(f"[AutoRetrain CleanupBackups] 已删除旧备份目录: {dir_to_delete}")
        except Exception as e:
            print(f"[AutoRetrain CleanupBackups] 清理旧备份时出错: {e}")

    def _record_training_result(self, target_name: str, success: bool, error_msg: Optional[str] = None):
        try:
            ts = datetime.now(pytz.UTC).isoformat() # 使用带时区的ISO格式
            
            if target_name not in self.training_history:
                self.training_history[target_name] = []
            
            self.training_history[target_name].append({
                'timestamp': ts, 'success': success, 'error_message': error_msg
            })
            # 限制历史记录长度
            self.training_history[target_name] = self.training_history[target_name][-self.max_model_history:]
            self._save_training_history()
        except Exception as e:
            print(f"[AutoRetrain RecordResult - {target_name}] 记录训练结果时出错: {e}")

    def _save_training_history(self):
        try:
            history_file_path = self.log_dir / "training_history.json"
            with open(history_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.training_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"[AutoRetrain SaveHistory] 保存训练历史到JSON时出错: {e}")

    def _cleanup_job(self):
        try:
            print(f"[AutoRetrain Cleanup] 开始执行数据和日志清理任务 at {datetime.now(self.scheduler.timezone if self.scheduler else pytz.UTC)}")
            self._cleanup_training_history() # 清理JSON中的训练历史
            
            # 清理所有目标模型的备份目录中的旧备份
            if self.model_backup_dir.exists():
                for target_model_main_dir_name in self.model_backup_dir.iterdir(): # e.g., trained_models_btc_15m_up_backup
                    if target_model_main_dir_name.is_dir():
                         self._cleanup_old_backups(target_model_main_dir_name) # 传递的是具体目标的备份根目录

            self._cleanup_old_logs() # 清理 .log 文件
            self._cleanup_temp_files() # 清理临时文件 (如果实现)
            print("[AutoRetrain Cleanup] 数据和日志清理任务完成。")
        except Exception as e:
            print(f"[AutoRetrain Cleanup] 清理任务执行失败: {e}")
            traceback.print_exc()

    def _cleanup_training_history(self):
        try:
            days_to_keep = self.data_retention_days_val # 使用实例属性
            cutoff_dt = datetime.now(pytz.UTC) - timedelta(days=days_to_keep)
            
            for target_name in list(self.training_history.keys()):
                valid_history = [
                    rec for rec in self.training_history[target_name]
                    if datetime.fromisoformat(rec['timestamp']) >= cutoff_dt
                ]
                if len(valid_history) < len(self.training_history[target_name]):
                    print(f"[AutoRetrain CleanupHistory - {target_name}] 清理了 {len(self.training_history[target_name]) - len(valid_history)} 条过期的训练历史记录。")
                self.training_history[target_name] = valid_history
            self._save_training_history() # 保存清理后的历史
        except Exception as e:
            print(f"[AutoRetrain CleanupHistory] 清理训练历史记录时出错: {e}")

    def _cleanup_old_logs(self):
        """清理 self.log_dir 中旧的 .log 文件"""
        try:
            if not self.log_dir.exists(): return
            days_to_keep = self.data_retention_days_val
            cutoff_timestamp_sec = (datetime.now(pytz.UTC) - timedelta(days=days_to_keep)).timestamp()
            
            for log_file_path in self.log_dir.glob('*.log'): # 只针对 .log 文件
                if log_file_path.is_file():
                    try:
                        if log_file_path.stat().st_mtime < cutoff_timestamp_sec:
                            log_file_path.unlink()
                            print(f"[AutoRetrain CleanupLogs] 已删除旧日志文件: {log_file_path}")
                    except Exception as e_file_op:
                        print(f"[AutoRetrain CleanupLogs] 删除日志文件 '{log_file_path}' 时出错: {e_file_op}")
        except Exception as e:
            print(f"[AutoRetrain CleanupLogs] 清理旧日志文件时发生错误: {e}")
            
    def _cleanup_temp_files(self):
        """清理工作目录下的临时文件（示例）"""
        # 这个函数需要根据您的项目实际产生的临时文件来具体实现
        # 例如，清理特定模式的文件或特定子目录
        print("[AutoRetrain CleanupTemp] 临时文件清理逻辑未完全实现。")
        # temp_dir_example = self.work_dir / "temp_outputs"
        # if temp_dir_example.exists():
        #     shutil.rmtree(temp_dir_example) # 危险操作，确保路径正确
        #     print(f"[AutoRetrain CleanupTemp] 已清理临时目录: {temp_dir_example}")

    def _log_scheduler_status(self):
        if self.scheduler and self.scheduler.running:
            jobs = self.scheduler.get_jobs()
            print(f"[AutoRetrain Status] 当前调度器中活跃任务数: {len(jobs)}")
            for job in jobs:
                next_run = job.next_run_time
                next_run_str = next_run.strftime('%Y-%m-%d %H:%M:%S %Z') if next_run else "N/A"
                print(f"  - Job: '{job.name}' (ID: {job.id}), Next Run: {next_run_str}")
        else:
            print("[AutoRetrain Status] 调度器未运行或未初始化。")
    
    def get_training_status(self) -> Dict[str, Any]:
        status_dict = {
            'is_running': self.is_running,
            'scheduler_active': self.scheduler.running if self.scheduler else False,
            'active_jobs_count': len(self.scheduler.get_jobs()) if self.scheduler else 0,
            'active_jobs_details': [],
            'last_successful_training_times': {},
            'last_failed_training_times': {},
            'training_history_summary_last5': {}
        }
        if self.scheduler:
            for job in self.scheduler.get_jobs():
                status_dict['active_jobs_details'].append({
                    'id': job.id, 'name': job.name, 
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None
                })
        for target, history_list in self.training_history.items():
            if history_list:
                successful_trains = [h for h in history_list if h.get('success')]
                failed_trains = [h for h in history_list if not h.get('success')]
                if successful_trains:
                    status_dict['last_successful_training_times'][target] = max(s['timestamp'] for s in successful_trains)
                if failed_trains:
                    status_dict['last_failed_training_times'][target] = max(f['timestamp'] for f in failed_trains)
                status_dict['training_history_summary_last5'][target] = history_list[-5:] # 最后5条记录
        return status_dict
    
    def force_retrain(self, target_name: str) -> bool:
        print(f"[AutoRetrain Force] 收到强制重训练请求: {target_name}")
        if not self._should_retrain(target_name): # 仍然检查基本条件，例如训练锁
             print(f"[AutoRetrain Force] {target_name} 不满足基本重训练条件（例如正在训练），强制重训练中止。")
             return False
        
        success = self._execute_retrain(target_name)
        self._record_training_result(target_name, success, error_msg="Forced Retrain")
        print(f"[AutoRetrain Force] {target_name} 强制重训练完成，结果: {'成功' if success else '失败'}")
        return success
    
    def update_config(self, new_config_dict: Dict[str, Any]):
        """通过字典更新内部的retrain_config并重启调度器（如果正在运行）"""
        try:
            print(f"[AutoRetrain UpdateConfig] 收到配置更新请求: {new_config_dict}")
            self.retrain_config.update(new_config_dict) # 更新内存中的配置
            
            # 将更新后的配置持久化到 auto_retrain_config.json
            work_dir_base = Path(getattr(auto_retrain_config, 'WORK_DIR', ".")) if auto_retrain_config else Path(".")
            json_config_file_path = work_dir_base / 'config' / 'auto_retrain_config.json'
            try:
                with open(json_config_file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.retrain_config, f, indent=2, ensure_ascii=False)
                print(f"[AutoRetrain UpdateConfig] JSON配置文件 '{json_config_file_path}' 已更新。")
            except Exception as e_json_save:
                print(f"[AutoRetrain UpdateConfig] 保存更新后的JSON配置失败: {e_json_save}")

            # 如果调度器正在运行，重启以应用新配置
            if self.is_running and self.scheduler:
                print("[AutoRetrain UpdateConfig] 调度器正在运行，将重启以应用新配置...")
                self.stop()
                time.sleep(1) # 给点时间停止
                self.start() # start会重新读取配置并添加作业
            else:
                print("[AutoRetrain UpdateConfig] 配置已更新，调度器未运行，下次启动时将使用新配置。")
        except Exception as e:
            print(f"[AutoRetrain UpdateConfig] 更新配置过程中发生错误: {e}")
            traceback.print_exc()

# 全局实例
_auto_retrain_scheduler_instance = None

def get_auto_retrain_scheduler() -> AutoRetrainScheduler:
    global _auto_retrain_scheduler_instance
    if _auto_retrain_scheduler_instance is None:
        # 尝试从主项目配置中获取 DynamicConfigManager (如果它被设计为全局共享)
        # 否则 AutoRetrainScheduler 会自己创建一个。
        dcm_from_main = None
        if hasattr(main, 'get_global_dynamic_config_manager'): # 假设main.py有这个函数
            try:
                dcm_from_main = main.get_global_dynamic_config_manager()
            except Exception: pass # 忽略错误，使用默认
        _auto_retrain_scheduler_instance = AutoRetrainScheduler(config_manager=dcm_from_main)
    return _auto_retrain_scheduler_instance

# 以下函数是对外暴露的接口
def start_auto_retrain():
    scheduler = get_auto_retrain_scheduler()
    scheduler.start()

def stop_auto_retrain():
    scheduler = get_auto_retrain_scheduler()
    scheduler.stop()

def get_retrain_status() -> Dict[str, Any]:
    scheduler = get_auto_retrain_scheduler()
    return scheduler.get_training_status()

def force_retrain_target(target_name: str) -> bool:
    scheduler = get_auto_retrain_scheduler()
    return scheduler.force_retrain(target_name)

def update_scheduler_config(new_config_values: Dict[str, Any]):
    """外部调用此函数以更新调度器的JSON配置"""
    scheduler = get_auto_retrain_scheduler()
    scheduler.update_config(new_config_values)


if __name__ == "__main__":
    print("--- 测试自动重训练调度器 (独立运行) ---")
    
    # 为了独立测试，确保主项目的config.py可以被找到，或者处理其缺失
    if not main_project_config:
        print("主项目config.py未找到，某些功能可能受限或使用默认值。")
        # 可以考虑创建一个临时的 main_project_config 存根对象用于测试
        class MockMainConfig:
            PREDICTION_TARGETS = [{"name": "TestTarget1", "interval": "1h", "symbol": "BTCUSDT", "target_variable_type": "UP_ONLY", "model_save_dir": "mock_models/test1"}]
            def get_target_config(self, name):
                if name == "TestTarget1": return {"name": "TestTarget1", "interval": "1h", "symbol": "BTCUSDT", "target_variable_type": "UP_ONLY", "model_save_dir": "mock_models/test1"}
                return {}
            SYMBOL = "BTCUSDT"
        main_project_config = MockMainConfig()

    # 也确保 main.py 中的 training_in_progress_flag 和 binance_client 有一个模拟版本
    if not hasattr(main, 'training_in_progress_flag'):
        main.training_in_progress_flag = threading.Event()
    if not hasattr(main, 'binance_client'):
        main.binance_client = None # 或者一个模拟的客户端
        print("警告: 未找到 main.binance_client，数据可用性检查可能失败或不准确。")
    if not hasattr(main, 'run_training_pipeline'):
        def mock_run_training_pipeline(target_type_filter=None):
            print(f"[MockMain] run_training_pipeline called with filter: {target_type_filter}")
            time.sleep(5) # 模拟训练耗时
            return True
        main.run_training_pipeline = mock_run_training_pipeline
        print("警告: 未找到 main.run_training_pipeline，使用模拟函数。")


    test_scheduler = get_auto_retrain_scheduler()
    
    print("\n当前JSON配置:")
    print(json.dumps(test_scheduler.retrain_config, indent=2, ensure_ascii=False))
    
    print("\n启动调度器...")
    test_scheduler.start()
    
    time.sleep(2) # 等待作业注册
    
    status_info = test_scheduler.get_training_status()
    print("\n调度器状态:")
    print(json.dumps(status_info, indent=2, ensure_ascii=False, default=str))

    # 模拟一段时间的运行
    try:
        print("\n调度器运行中... 按 Ctrl+C 停止。")
        while test_scheduler.is_running:
            time.sleep(10)
            # 可以定期打印状态或模拟性能变化
            # print(f"\n当前时间: {datetime.now()}")
            # current_status = test_scheduler.get_training_status()
            # print(f"  活跃作业: {current_status['active_jobs_count']}")
            pass # 主循环保持，等待调度器任务
    except KeyboardInterrupt:
        print("\n用户中断测试。")
    finally:
        print("\n停止调度器...")
        test_scheduler.stop()
        print("调度器已停止。")

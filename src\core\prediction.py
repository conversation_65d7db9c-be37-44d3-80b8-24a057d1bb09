# prediction.py

import warnings
# 屏蔽LightGBM的警告信息
warnings.filterwarnings('ignore', category=UserWarning, module='lightgbm')
warnings.filterwarnings('ignore', message='.*No further splits with positive gain.*')
warnings.filterwarnings('ignore', message='.*best gain.*')

import requests
import numpy as np
import os
import joblib
import pandas as pd
from datetime import datetime, timedelta, timezone
import time
import traceback
import threading
import pandas_ta as pta  # 临时注释，解决numpy兼容性问题
import json
import math
import atexit # 用于注册退出处理函数
from collections import defaultdict # 导入 defaultdict
try:
    import shap
    import matplotlib.pyplot as plt
    import seaborn as sns
    SHAP_AVAILABLE = True
except ImportError:
    shap = None
    plt = None
    sns = None
    SHAP_AVAILABLE = False

from . import data_utils
import config
from .data_utils import fetch_binance_history, prepare_features_for_prediction, interval_to_timedelta
from . import gui
from .gui import update_status, update_prediction_display, update_gui_safe, _gui_root
from lightgbm import LGBMClassifier
from binance.exceptions import BinanceAPIException, BinanceRequestException
import requests.exceptions
from sklearn.calibration import CalibratedClassifierCV
# from sklearn.metrics import brier_score_loss # 主要在训练后评估，预测时可选
import pygame
import multiprocessing
import tkinter as tk
from lightgbm import early_stopping # 或者如果您已经导入了整个 lightgbm (import lightgbm as lgb)，则使用 lgb.early_stopping
from sklearn.metrics import accuracy_score, log_loss, classification_report
from ..utils.DataValidator import DataValidator
from . import realtime_data_manager
from ..utils.dynamic_config_manager import DynamicConfigManager # 导入新的管理器
from ..analysis.analysis_logger import log_prediction_context, initialize_loggers
import optuna
from sklearn.model_selection import TimeSeriesSplit # 用于Optuna内部的CV
from sklearn.metrics import f1_score, make_scorer # 用于计算F1分数

# --- 元模型智能决策函数 ---
def _make_intelligent_meta_decision(meta_probas, original_class):
    """
    基于元模型概率输出进行智能决策，避免过度依赖argmax导致的频繁中性预测

    Args:
        meta_probas: 元模型输出的概率数组 [P(下跌), P(上涨), P(中性)]
        original_class: 原始的argmax类别预测

    Returns:
        tuple: (final_signal, prediction_label, prediction_color)
    """
    # 获取配置参数
    up_threshold = getattr(config, 'META_SIGNAL_UP_THRESHOLD', 0.4)
    down_threshold = getattr(config, 'META_SIGNAL_DOWN_THRESHOLD', 0.4)
    confidence_gap = getattr(config, 'META_SIGNAL_CONFIDENCE_GAP', 0.1)
    use_confidence_gap = getattr(config, 'META_SIGNAL_USE_CONFIDENCE_GAP', True)

    # 解析概率（假设：0=DOWN, 1=UP, 2=NEUTRAL）
    p_down = meta_probas[0] if len(meta_probas) > 0 else 0.33
    p_up = meta_probas[1] if len(meta_probas) > 1 else 0.33
    p_neutral = meta_probas[2] if len(meta_probas) > 2 else 0.33

    print(f"    智能决策分析: P(下跌)={p_down:.3f}, P(上涨)={p_up:.3f}, P(中性)={p_neutral:.3f}")
    print(f"    决策阈值: UP>={up_threshold}, DOWN>={down_threshold}, 置信度差值>={confidence_gap}")

    # 决策逻辑
    decision_reasons = []

    # 检查上涨信号
    if p_up >= up_threshold:
        decision_reasons.append(f"P(上涨)={p_up:.3f}>={up_threshold}")
        if use_confidence_gap:
            confidence_diff = p_up - p_neutral
            if confidence_diff >= confidence_gap:
                decision_reasons.append(f"置信度差值={confidence_diff:.3f}>={confidence_gap}")
                final_signal = "UP_Meta"
                prediction_label = f"上涨 (P:{p_up:.2%}, 置信度+{confidence_diff:.2%})"
                prediction_color = config.UP_COLOR
                print(f"    决策结果: 做多 - {', '.join(decision_reasons)}")
                return final_signal, prediction_label, prediction_color
            else:
                decision_reasons.append(f"但置信度差值={confidence_diff:.3f}<{confidence_gap}")
        else:
            final_signal = "UP_Meta"
            prediction_label = f"上涨 (P:{p_up:.2%})"
            prediction_color = config.UP_COLOR
            print(f"    决策结果: 做多 - {', '.join(decision_reasons)}")
            return final_signal, prediction_label, prediction_color

    # 检查下跌信号
    if p_down >= down_threshold:
        decision_reasons.append(f"P(下跌)={p_down:.3f}>={down_threshold}")
        if use_confidence_gap:
            confidence_diff = p_down - p_neutral
            if confidence_diff >= confidence_gap:
                decision_reasons.append(f"置信度差值={confidence_diff:.3f}>={confidence_gap}")
                final_signal = "DOWN_Meta"
                prediction_label = f"下跌 (P:{p_down:.2%}, 置信度+{confidence_diff:.2%})"
                prediction_color = config.DOWN_COLOR
                print(f"    决策结果: 做空 - {', '.join(decision_reasons)}")
                return final_signal, prediction_label, prediction_color
            else:
                decision_reasons.append(f"但置信度差值={confidence_diff:.3f}<{confidence_gap}")
        else:
            final_signal = "DOWN_Meta"
            prediction_label = f"下跌 (P:{p_down:.2%})"
            prediction_color = config.DOWN_COLOR
            print(f"    决策结果: 做空 - {', '.join(decision_reasons)}")
            return final_signal, prediction_label, prediction_color

    # 默认为中性
    neutral_reasons = []
    if p_up < up_threshold:
        neutral_reasons.append(f"P(上涨)={p_up:.3f}<{up_threshold}")
    if p_down < down_threshold:
        neutral_reasons.append(f"P(下跌)={p_down:.3f}<{down_threshold}")

    final_signal = "Neutral_Meta"
    prediction_label = f"中性 (P:{p_neutral:.2%}, 原始类别:{original_class})"
    prediction_color = config.NEUTRAL_COLOR

    print(f"    决策结果: 观望 - {', '.join(neutral_reasons)}")
    if decision_reasons:
        print(f"    未满足条件: {', '.join(decision_reasons)}")

    return final_signal, prediction_label, prediction_color


# --- 状态管理类 ---
class PredictionStateManager:
    def __init__(self):
        self._lock = threading.Lock()
        # 初始化 last_signal_state，确保 config 和 PREDICTION_TARGETS 可访问
        # 如果在类定义时 config 不可用，可能需要在外部初始化后传入或稍后设置
        try:
            self._last_signal_state = {target['name']: "Neutral" for target in config.PREDICTION_TARGETS if isinstance(target, dict) and 'name' in target}
        except (NameError, AttributeError):
             # Fallback if config or PREDICTION_TARGETS isn't available at this exact point
            print("Warning: config.PREDICTION_TARGETS not available at PredictionStateManager init for _last_signal_state. Initializing as empty dict.")
            self._last_signal_state = {}
        self._last_signal_alert_time = 0
        self._last_pre_alarm_play_time = defaultdict(float) # 使用defaultdict简化时间戳处理
        self._last_signal_sent_time_per_target = defaultdict(float)
        self._last_signal_type_sent_per_target = defaultdict(str)
        self._strategy_execution_counters = defaultdict(int)
        self._music_fadeout_timer = None
        # prediction_context_logger 通常是日志记录器实例，其自身的并发由logging模块处理
        # 如果需要动态更改记录器实例，则需要锁
        self._prediction_context_logger = None 

        # 元模型相关状态
        self._meta_model_instance = None
        self._meta_model_feature_names = None
        self._meta_model_loaded_successfully = False
        self._last_meta_predictions = {}  # 存储上一次的元模型基础预测概率

    # --- Getters and Setters for thread-safe access ---
    # Example for last_signal_alert_time
    def get_last_signal_alert_time(self):
        with self._lock:
            return self._last_signal_alert_time

    def set_last_signal_alert_time(self, value):
        with self._lock:
            self._last_signal_alert_time = value

    # Example for last_pre_alarm_play_time (dictionary)
    def get_last_pre_alarm_play_time(self, key):
        with self._lock:
            return self._last_pre_alarm_play_time[key]

    def set_last_pre_alarm_play_time(self, key, value):
        with self._lock:
            self._last_pre_alarm_play_time[key] = value
            
    def get_all_last_pre_alarm_play_time(self):
        with self._lock:
            return self._last_pre_alarm_play_time.copy() # 返回副本以防外部修改

    # last_signal_state
    def get_last_signal_state(self, target_name):
        with self._lock:
            return self._last_signal_state.get(target_name, "Neutral")

    def set_last_signal_state(self, target_name, state):
        with self._lock:
            self._last_signal_state[target_name] = state
            
    def get_all_last_signal_states(self):
        with self._lock:
            return self._last_signal_state.copy()
    
    def initialize_last_signal_state(self, targets_config):
        with self._lock:
            self._last_signal_state = {target['name']: "Neutral" for target in targets_config if isinstance(target, dict) and 'name' in target}

    # last_signal_sent_time_per_target
    def get_last_signal_sent_time(self, target_name):
        with self._lock:
            return self._last_signal_sent_time_per_target[target_name]

    def set_last_signal_sent_time(self, target_name, timestamp):
        with self._lock:
            self._last_signal_sent_time_per_target[target_name] = timestamp
            
    def get_all_last_signal_sent_times(self):
        with self._lock:
            return self._last_signal_sent_time_per_target.copy()

    # last_signal_type_sent_per_target
    def get_last_signal_type_sent(self, target_name):
        with self._lock:
            return self._last_signal_type_sent_per_target[target_name]

    def set_last_signal_type_sent(self, target_name, signal_type):
        with self._lock:
            self._last_signal_type_sent_per_target[target_name] = signal_type
            
    def get_all_last_signal_types_sent(self):
        with self._lock:
            return self._last_signal_type_sent_per_target.copy()

    # strategy_execution_counters
    def get_strategy_execution_counter(self, strategy_name):
        with self._lock:
            return self._strategy_execution_counters[strategy_name]

    def increment_strategy_execution_counter(self, strategy_name):
        with self._lock:
            self._strategy_execution_counters[strategy_name] += 1
            return self._strategy_execution_counters[strategy_name]

    def reset_strategy_execution_counter(self, strategy_name):
        with self._lock:
            self._strategy_execution_counters[strategy_name] = 0
            
    def get_all_strategy_execution_counters(self):
        with self._lock:
            return self._strategy_execution_counters.copy()

    # music_fadeout_timer
    def get_music_fadeout_timer(self):
        with self._lock:
            return self._music_fadeout_timer

    def set_music_fadeout_timer(self, timer_instance):
        # threading.Timer objects are typically not modified after creation,
        # but the reference to it is. If it can be cancelled/recreated, lock is good.
        with self._lock:
            if self._music_fadeout_timer and hasattr(self._music_fadeout_timer, 'cancel'):
                try:
                    self._music_fadeout_timer.cancel()
                except Exception:
                    pass # Ignore if already run or cancelled
            self._music_fadeout_timer = timer_instance

    # prediction_context_logger (assuming it might be reassigned)
    def get_prediction_context_logger(self):
        # Logging instances are typically thread-safe for writing.
        # Lock is only needed if the logger instance itself is being swapped out.
        with self._lock: 
            return self._prediction_context_logger

    def set_prediction_context_logger(self, logger_instance):
        with self._lock:
            self._prediction_context_logger = logger_instance

    # Meta Model State
    def get_meta_model_instance(self):
        with self._lock:
            return self._meta_model_instance

    def set_meta_model_instance(self, instance):
        with self._lock:
            self._meta_model_instance = instance

    def get_meta_model_feature_names(self):
        with self._lock:
            return self._meta_model_feature_names

    def set_meta_model_feature_names(self, names):
        with self._lock:
            self._meta_model_feature_names = names

    def is_meta_model_loaded_successfully(self):
        with self._lock:
            return self._meta_model_loaded_successfully

    def set_meta_model_loaded_successfully(self, status):
        with self._lock:
            self._meta_model_loaded_successfully = status

    def get_last_meta_predictions(self):
        with self._lock:
            return self._last_meta_predictions.copy()

    def set_last_meta_predictions(self, predictions_dict):
        with self._lock:
            self._last_meta_predictions = predictions_dict.copy() if predictions_dict else {}

    # --- Method Aliases ---
    update_last_signal_alert_time = set_last_signal_alert_time
    update_last_pre_alarm_play_time = set_last_pre_alarm_play_time
    update_last_signal_state = set_last_signal_state
    update_last_signal_sent_time = set_last_signal_sent_time
    update_last_signal_type_sent = set_last_signal_type_sent
    update_music_fadeout_timer = set_music_fadeout_timer
    update_prediction_context_logger = set_prediction_context_logger
    update_meta_model_instance = set_meta_model_instance
    update_meta_model_feature_names = set_meta_model_feature_names
    update_meta_model_loaded_successfully = set_meta_model_loaded_successfully


# --- 全局状态管理器实例 ---
# prediction_lock 仍然可以作为全局锁用于控制整个预测流程的入口，如果需要的话
prediction_lock = threading.Lock()
global_prediction_state_manager = PredictionStateManager()

# --- 元模型相关文件名 (从config获取，确保config已导入) ---
# 这些可以硬编码，或者更好的是，也从 config.py 中读取，如果它们是固定的
META_MODEL_FILENAME = "meta_model_lgbm.joblib"
META_FEATURES_FILENAME = "meta_model_features.json"

# --- 全局配置读取 ---
SEND_SIGNALS_TO_SIMULATOR = getattr(config, 'SIMULATOR_INTEGRATION_ENABLED', True)
# --------------------
SIGNAL_SEND_COOLDOWN_SECONDS = getattr(config, 'SIGNAL_SEND_COOLDOWN_SECONDS', 120)


# --- Pygame Mixer 初始化 ---
_PYGAME_MIXER_INIT_ATTEMPTED_IN_THIS_PROCESS = False
PYGAME_MIXER_AVAILABLE = False
if not _PYGAME_MIXER_INIT_ATTEMPTED_IN_THIS_PROCESS:
    _PYGAME_MIXER_INIT_ATTEMPTED_IN_THIS_PROCESS = True
    is_main_process_for_init = (multiprocessing.current_process().name == 'MainProcess')
    if is_main_process_for_init:
        if 'PYGAME_HIDE_SUPPORT_PROMPT' not in os.environ:
            os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = "1"
        if 'SDL_VIDEODRIVER' not in os.environ and not os.name == 'nt':
             try: pass # os.environ['SDL_VIDEODRIVER'] = 'dummy'
             except Exception: pass
    try:
        pygame.mixer.pre_init(44100, -16, 2, 512)
        pygame.mixer.init()
        PYGAME_MIXER_AVAILABLE = pygame.mixer.get_init() is not None
        if not PYGAME_MIXER_AVAILABLE: pygame.init(); PYGAME_MIXER_AVAILABLE = pygame.mixer.get_init() is not None
        if PYGAME_MIXER_AVAILABLE and is_main_process_for_init: print("[Pygame Init] Pygame mixer 初始化成功。")
        elif not PYGAME_MIXER_AVAILABLE and is_main_process_for_init: print("!!! [Pygame Init] Pygame mixer 初始化失败。")
    except ImportError:
        if is_main_process_for_init: print("!!! 警告: 'pygame' 库未找到。")
    except Exception as e_pygame_init:
        if is_main_process_for_init: print(f"!!! 警告: 初始化 Pygame 时发生错误: {e_pygame_init}。")

# --- 模拟盘与冷却配置 ---
SIMULATOR_API_URL = getattr(config, 'SIMULATOR_API_URL', "http://127.0.0.1:5005/signal") # 从config读取，提供默认
SEND_SIGNALS_TO_SIMULATOR = getattr(config, 'SIMULATOR_INTEGRATION_ENABLED', True)
SIGNAL_SEND_COOLDOWN_SECONDS = getattr(config, 'SIGNAL_SEND_COOLDOWN_SECONDS', 120)
# DEFAULT_AMOUNT_FOR_SIMULATOR_SIGNAL 似乎不再直接使用，因为金额由策略决定

VALID_LGBM_PARAM_KEYS_PRED_PY = [
    'boosting_type', 'num_leaves', 'max_depth', 'learning_rate', 'n_estimators',
    'subsample_for_bin', 'objective', 'class_weight', 'min_split_gain',
    'min_child_weight', 'min_child_samples', 'subsample', 'subsample_freq',
    'colsample_bytree', 'reg_alpha', 'reg_lambda', 'random_state', 'n_jobs',
    'importance_type', 'metric', 'device_type', 'verbose', 'verbosity', 'seed', 
    'callbacks', 'eval_set', 'eval_names', 'eval_metric', 'feature_name', 
    'categorical_feature', 'early_stopping_rounds', 'first_metric_only', 'num_class'
]





# --- 用于Optuna内部评估的辅助函数 (可以放在 train_meta_model 外部或作为其内部函数) ---
def apply_realtime_meta_feature_engineering(meta_input_data_dict, trained_feature_names):
    """
    在实时预测时应用元模型特征工程，确保与训练时的特征一致

    参数:
    - meta_input_data_dict: 包含基础模型概率的字典
    - trained_feature_names: 训练时使用的特征名列表

    返回:
    - 添加了特征工程的字典
    """
    print("  应用实时元模型特征工程...")

    # 创建副本以避免修改原始数据
    enhanced_data = meta_input_data_dict.copy()

    # 识别UP和DOWN模型的概率特征 - 只选择真正的OOF概率特征
    up_prob_features = [key for key in enhanced_data.keys()
                       if ('UP' in key.upper() and
                           (key.startswith('oof_proba_') or key.startswith('prob_')))]
    down_prob_features = [key for key in enhanced_data.keys()
                         if ('DOWN' in key.upper() and
                             (key.startswith('oof_proba_') or key.startswith('prob_')))]

    print(f"    识别到UP概率特征: {up_prob_features}")
    print(f"    识别到DOWN概率特征: {down_prob_features}")

    # 1. 基础模型概率差异 (Probability Difference)
    if len(up_prob_features) >= 1 and len(down_prob_features) >= 1:
        up_prob = enhanced_data[up_prob_features[0]] if len(up_prob_features) == 1 else np.mean([enhanced_data[key] for key in up_prob_features])
        down_prob = enhanced_data[down_prob_features[0]] if len(down_prob_features) == 1 else np.mean([enhanced_data[key] for key in down_prob_features])

        enhanced_data['meta_prob_diff_up_vs_down'] = up_prob - down_prob
        print(f"    ✓ 添加特征: meta_prob_diff_up_vs_down = {enhanced_data['meta_prob_diff_up_vs_down']:.3f}")

        # 2. 基础模型概率总和 (Combined Conviction)
        enhanced_data['meta_prob_sum_up_down'] = up_prob + down_prob
        print(f"    ✓ 添加特征: meta_prob_sum_up_down = {enhanced_data['meta_prob_sum_up_down']:.3f}")
    else:
        print("    ! 警告: 未找到足够的UP/DOWN概率特征，跳过概率差异和总和特征")

    # 3. 滞后特征和变化特征 - 实时预测时需要从历史状态获取
    # 获取上一次的预测概率（如果有的话）
    last_meta_predictions = global_prediction_state_manager.get_last_meta_predictions()

    # 为每个基础概率特征添加滞后和变化特征 - 只处理真正的概率特征
    for key in list(enhanced_data.keys()):
        # 只处理真正的概率特征，与训练时的逻辑保持一致
        if (key.startswith('oof_proba_') or key.startswith('prob_') or
            (key.startswith('meta_') and ('prob' in key))):
            current_prob = enhanced_data[key]

            # 滞后特征
            lag_feature_name = f"meta_lag1_{key}"
            if last_meta_predictions and key in last_meta_predictions:
                lag_value = last_meta_predictions[key]
            else:
                # 如果没有历史数据，使用当前值的中位数估计（这里用当前值本身）
                lag_value = current_prob
            enhanced_data[lag_feature_name] = lag_value
            print(f"    ✓ 添加滞后特征: {lag_feature_name} = {lag_value:.3f}")

            # 变化特征
            change_feature_name = f"meta_change1_{key}"
            if last_meta_predictions and key in last_meta_predictions:
                change_value = current_prob - last_meta_predictions[key]
            else:
                # 如果没有历史数据，变化为0
                change_value = 0.0
            enhanced_data[change_feature_name] = change_value
            print(f"    ✓ 添加变化特征: {change_feature_name} = {change_value:.3f}")

    # 保存当前预测概率供下次使用 - 只保存真正的概率特征
    current_predictions = {key: val for key, val in enhanced_data.items()
                          if (key.startswith('oof_proba_') or key.startswith('prob_') or
                              (key.startswith('meta_') and ('prob' in key)))}
    global_prediction_state_manager.set_last_meta_predictions(current_predictions)

    # 🎯 核心优化建议2.4：增强的上下文感知特征工程
    feature_engineering_config = getattr(config, 'META_MODEL_FEATURE_ENGINEERING_CONFIG', {})

    # 1. 基础模型间的分歧度特征
    if feature_engineering_config.get('enable_model_divergence', True):
        if len(up_prob_features) >= 2:
            up_probs = [enhanced_data[feat] for feat in up_prob_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if len(up_probs) >= 2:
                enhanced_data['up_models_divergence'] = np.std(up_probs)
                enhanced_data['up_models_max_diff'] = max(up_probs) - min(up_probs)
                enhanced_data['up_models_range'] = max(up_probs) - min(up_probs)
                enhanced_data['up_models_mean'] = np.mean(up_probs)
            else:
                enhanced_data['up_models_divergence'] = 0.0
                enhanced_data['up_models_max_diff'] = 0.0
                enhanced_data['up_models_range'] = 0.0
                enhanced_data['up_models_mean'] = 0.5
        else:
            enhanced_data['up_models_divergence'] = 0.0
            enhanced_data['up_models_max_diff'] = 0.0
            enhanced_data['up_models_range'] = 0.0
            enhanced_data['up_models_mean'] = 0.5

        if len(down_prob_features) >= 2:
            down_probs = [enhanced_data[feat] for feat in down_prob_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if len(down_probs) >= 2:
                enhanced_data['down_models_divergence'] = np.std(down_probs)
                enhanced_data['down_models_max_diff'] = max(down_probs) - min(down_probs)
                enhanced_data['down_models_range'] = max(down_probs) - min(down_probs)
                enhanced_data['down_models_mean'] = np.mean(down_probs)
            else:
                enhanced_data['down_models_divergence'] = 0.0
                enhanced_data['down_models_max_diff'] = 0.0
                enhanced_data['down_models_range'] = 0.0
                enhanced_data['down_models_mean'] = 0.5
        else:
            enhanced_data['down_models_divergence'] = 0.0
            enhanced_data['down_models_max_diff'] = 0.0
            enhanced_data['down_models_range'] = 0.0
            enhanced_data['down_models_mean'] = 0.5

    # 2. 模型置信度聚合特征
    if feature_engineering_config.get('enable_confidence_features', True):
        confidence_features = [key for key in enhanced_data.keys() if 'confidence' in key.lower()]
        if confidence_features:
            confidence_values = [enhanced_data[feat] for feat in confidence_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if confidence_values:
                enhanced_data['meta_avg_confidence'] = np.mean(confidence_values)
                enhanced_data['meta_max_confidence'] = np.max(confidence_values)
                enhanced_data['meta_min_confidence'] = np.min(confidence_values)
                enhanced_data['meta_confidence_spread'] = np.max(confidence_values) - np.min(confidence_values)
            else:
                enhanced_data['meta_avg_confidence'] = 0.0
                enhanced_data['meta_max_confidence'] = 0.0
                enhanced_data['meta_min_confidence'] = 0.0
                enhanced_data['meta_confidence_spread'] = 0.0

    # 3. 市场状态聚合特征
    if feature_engineering_config.get('enable_market_state_features', True):
        atr_features = [key for key in enhanced_data.keys() if 'atr' in key.lower()]
        adx_features = [key for key in enhanced_data.keys() if 'adx' in key.lower()]
        rsi_features = [key for key in enhanced_data.keys() if 'rsi' in key.lower()]

        if atr_features:
            atr_values = [enhanced_data[feat] for feat in atr_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if atr_values:
                enhanced_data['meta_avg_atr'] = np.mean(atr_values)
                enhanced_data['meta_volatility_consistency'] = 1.0 / (1.0 + np.std(atr_values))  # 波动率一致性

        if adx_features:
            adx_values = [enhanced_data[feat] for feat in adx_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if adx_values:
                enhanced_data['meta_avg_adx'] = np.mean(adx_values)
                enhanced_data['meta_trend_consistency'] = 1.0 / (1.0 + np.std(adx_values))  # 趋势一致性

        if rsi_features:
            rsi_values = [enhanced_data[feat] for feat in rsi_features if not pd.isna(enhanced_data.get(feat, np.nan))]
            if rsi_values:
                enhanced_data['meta_avg_rsi'] = np.mean(rsi_values)
                enhanced_data['meta_rsi_extremeness'] = abs(np.mean(rsi_values) - 50.0) / 50.0  # RSI极端程度

    # 4. 交互特征
    if feature_engineering_config.get('enable_interaction_features', True):
        # 概率×置信度交互
        for prob_feat in up_prob_features + down_prob_features:
            if prob_feat in enhanced_data:
                prob_val = enhanced_data[prob_feat]
                # 寻找对应的置信度特征
                for conf_feat in confidence_features:
                    if conf_feat in enhanced_data:
                        conf_val = enhanced_data[conf_feat]
                        interaction_name = f'interaction_{prob_feat}_x_{conf_feat}'
                        enhanced_data[interaction_name] = prob_val * conf_val

        # 概率差异特征
        if len(up_prob_features) >= 1 and len(down_prob_features) >= 1:
            up_prob = enhanced_data.get(up_prob_features[0], 0.5)
            down_prob = enhanced_data.get(down_prob_features[0], 0.5)
            enhanced_data['meta_prob_diff_up_vs_down'] = up_prob - down_prob
            enhanced_data['meta_prob_sum_up_down'] = up_prob + down_prob
            enhanced_data['meta_prob_ratio_up_vs_down'] = up_prob / (down_prob + 1e-8)

    # 确保所有训练时的特征都存在，缺失的用默认值填充
    for feature_name in trained_feature_names:
        if feature_name not in enhanced_data:
            if 'prob' in feature_name.lower():
                default_value = 0.5
            else:
                default_value = 0.0
            enhanced_data[feature_name] = default_value
            print(f"    ! 填充缺失特征: {feature_name} = {default_value}")

    print(f"  ✓ 实时特征工程完成，特征数量: {len(enhanced_data)}")
    return enhanced_data


def perform_shap_analysis(trained_model, X_val, y_val, feature_names, save_dir):
    """
    执行SHAP可解释性分析

    Args:
        trained_model: 训练好的LightGBM模型
        X_val: 验证集特征数据 (numpy array)
        y_val: 验证集标签数据 (numpy array)
        feature_names: 特征名称列表
        save_dir: 保存目录

    Returns:
        dict: SHAP分析结果
    """
    if not SHAP_AVAILABLE:
        print("  ⚠️ SHAP库不可用，跳过SHAP分析")
        return None

    if X_val is None or len(X_val) == 0:
        print("  ⚠️ 验证集数据为空，跳过SHAP分析")
        return None

    try:
        print(f"  开始SHAP分析，验证集样本数: {len(X_val)}, 特征数: {len(feature_names)}")

        # 创建SHAP图表保存目录
        shap_plots_dir = os.path.join(save_dir, "shap_plots")
        os.makedirs(shap_plots_dir, exist_ok=True)

        # 限制样本数量以提高计算效率（最多使用1000个样本）
        max_samples = min(1000, len(X_val))
        if len(X_val) > max_samples:
            print(f"  为提高计算效率，从 {len(X_val)} 个样本中随机选择 {max_samples} 个进行SHAP分析")
            import numpy as np
            indices = np.random.choice(len(X_val), max_samples, replace=False)
            X_val_sample = X_val[indices]
            y_val_sample = y_val[indices] if y_val is not None else None
        else:
            X_val_sample = X_val
            y_val_sample = y_val

        # 创建DataFrame用于SHAP分析
        X_val_df = pd.DataFrame(X_val_sample, columns=feature_names)

        # 1. 创建SHAP TreeExplainer
        print("  创建SHAP TreeExplainer...")
        explainer = shap.TreeExplainer(trained_model)

        # 2. 计算SHAP值
        print("  计算SHAP值...")
        shap_values = explainer.shap_values(X_val_df)

        # 对于多分类问题，shap_values是一个列表，每个类别一个数组
        # 但有时LightGBM多分类也可能返回3D数组而不是列表
        if isinstance(shap_values, list):
            print(f"  多分类SHAP值计算完成，类别数: {len(shap_values)}")
            # 计算平均绝对SHAP值（所有类别的平均）
            mean_abs_shap = np.mean([np.abs(sv).mean(axis=0) for sv in shap_values], axis=0)
        elif hasattr(shap_values, 'shape') and len(shap_values.shape) == 3:
            print(f"  多分类SHAP值计算完成 (3D数组)，类别数: {shap_values.shape[2]}")
            # 转换为列表格式以保持一致性
            shap_values = [shap_values[:, :, i] for i in range(shap_values.shape[2])]
            mean_abs_shap = np.mean([np.abs(sv).mean(axis=0) for sv in shap_values], axis=0)
        else:
            print("  二分类SHAP值计算完成")
            mean_abs_shap = np.abs(shap_values).mean(axis=0)

        # 确保mean_abs_shap是一维数组
        if mean_abs_shap.ndim > 1:
            mean_abs_shap = mean_abs_shap.flatten()

        # 3. 特征重要性排序
        feature_importance = [(feature_names[i], float(mean_abs_shap[i])) for i in range(len(feature_names))]
        feature_importance.sort(key=lambda x: x[1], reverse=True)

        print("  前10个最重要特征:")
        for i, (feat_name, importance) in enumerate(feature_importance[:10]):
            print(f"    {i+1:2d}. {feat_name:<35} : {importance:.6f}")

        # 4. 生成SHAP图表
        analysis_results = {
            'feature_importance': feature_importance,
            'top_10_features': feature_importance[:10],
            'shap_plots_saved': [],
            'analysis_summary': {
                'total_features': len(feature_names),
                'samples_analyzed': len(X_val_sample),
                'model_type': 'LightGBM',
                'classes': len(shap_values) if isinstance(shap_values, list) else 2
            }
        }

        # 设置matplotlib后端和样式
        plt.style.use('default')
        plt.rcParams['font.size'] = 10
        plt.rcParams['figure.dpi'] = 100

        # 5. 生成并保存SHAP图表
        try:
            print("  生成SHAP图表...")

            # 设置中文字体支持
            try:
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass  # 如果字体设置失败，继续使用默认字体

            # 5.1 特征重要性条形图
            print("    生成特征重要性图...")
            plt.figure(figsize=(12, 8))
            top_features = feature_importance[:15]  # 显示前15个特征
            feat_names = [f[0] for f in top_features]
            feat_values = [f[1] for f in top_features]

            # 创建水平条形图
            y_pos = range(len(feat_names))
            bars = plt.barh(y_pos, feat_values, color='skyblue', alpha=0.7)

            # 设置标签和标题
            plt.yticks(y_pos, feat_names)
            plt.xlabel('平均绝对SHAP值')
            plt.title('元模型特征重要性 (基于SHAP值)', fontsize=14, fontweight='bold')
            plt.gca().invert_yaxis()  # 最重要的特征在顶部

            # 添加数值标签
            for i, (bar, value) in enumerate(zip(bars, feat_values)):
                plt.text(value + max(feat_values) * 0.01, bar.get_y() + bar.get_height()/2,
                        f'{value:.4f}', va='center', fontsize=9)

            plt.tight_layout()
            importance_plot_path = os.path.join(shap_plots_dir, "feature_importance.png")
            plt.savefig(importance_plot_path, dpi=150, bbox_inches='tight')
            plt.close()
            analysis_results['shap_plots_saved'].append(importance_plot_path)
            print(f"    ✓ 特征重要性图已保存: {importance_plot_path}")

            # 5.2 SHAP Summary Plots (散点/小提琴图) for each class
            if len(X_val_sample) <= 500:  # 限制样本数以避免图表过于复杂
                print("    生成SHAP summary plots...")

                if isinstance(shap_values, list):
                    # 多分类：为每个类别生成Summary Plot
                    class_names = ["明确下跌_元", "明确上涨_元", "中性_元"] if len(shap_values) >= 3 else [f"Class {i}" for i in range(len(shap_values))]

                    # Class 1 ("明确上涨_元") Summary Plot
                    if len(shap_values) > 1:
                        print("      生成Class 1 (明确上涨_元) Summary Plot...")
                        plt.figure(figsize=(12, 8))
                        shap.summary_plot(shap_values[1], X_val_df, show=False, max_display=15)
                        plt.title('SHAP Summary Plot - 明确上涨_元 (Class 1)', fontsize=14, fontweight='bold')
                        summary_plot_class1_path = os.path.join(shap_plots_dir, "shap_summary_plot_class1_up.png")
                        plt.savefig(summary_plot_class1_path, dpi=150, bbox_inches='tight')
                        plt.close()
                        analysis_results['shap_plots_saved'].append(summary_plot_class1_path)
                        print(f"      ✓ Class 1 Summary Plot已保存: {summary_plot_class1_path}")

                    # Class 0 ("明确下跌_元") Summary Plot
                    if len(shap_values) > 0:
                        print("      生成Class 0 (明确下跌_元) Summary Plot...")
                        plt.figure(figsize=(12, 8))
                        shap.summary_plot(shap_values[0], X_val_df, show=False, max_display=15)
                        plt.title('SHAP Summary Plot - 明确下跌_元 (Class 0)', fontsize=14, fontweight='bold')
                        summary_plot_class0_path = os.path.join(shap_plots_dir, "shap_summary_plot_class0_down.png")
                        plt.savefig(summary_plot_class0_path, dpi=150, bbox_inches='tight')
                        plt.close()
                        analysis_results['shap_plots_saved'].append(summary_plot_class0_path)
                        print(f"      ✓ Class 0 Summary Plot已保存: {summary_plot_class0_path}")

                else:
                    # 二分类
                    plt.figure(figsize=(12, 8))
                    shap.summary_plot(shap_values, X_val_df, show=False, max_display=15)
                    plt.title('SHAP Summary Plot', fontsize=14, fontweight='bold')
                    summary_plot_path = os.path.join(shap_plots_dir, "shap_summary_plot.png")
                    plt.savefig(summary_plot_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    analysis_results['shap_plots_saved'].append(summary_plot_path)
                    print(f"    ✓ SHAP summary plot已保存: {summary_plot_path}")

            # 5.3 单个样本的Waterfall Plot（选择一个有趣的样本）
            print("    生成SHAP waterfall plot...")
            sample_idx = 0  # 选择第一个样本

            try:
                if isinstance(shap_values, list):
                    # 多分类：为上涨类别（类别1）生成waterfall plot
                    class_idx = 1  # 上涨类别
                    expected_value = explainer.expected_value[class_idx]
                    shap_vals_sample = shap_values[class_idx][sample_idx]
                    class_name = "上涨类别"
                else:
                    expected_value = explainer.expected_value
                    shap_vals_sample = shap_values[sample_idx]
                    class_name = "预测"

                plt.figure(figsize=(12, 8))

                # 创建SHAP Explanation对象
                explanation = shap.Explanation(
                    values=shap_vals_sample,
                    base_values=expected_value,
                    data=X_val_sample[sample_idx],
                    feature_names=feature_names
                )

                # 生成waterfall plot
                shap.plots.waterfall(explanation, max_display=15, show=False)
                plt.title(f'SHAP Waterfall Plot - 样本 {sample_idx+1} ({class_name})', fontsize=14, fontweight='bold')

                waterfall_plot_path = os.path.join(shap_plots_dir, "shap_waterfall_plot.png")
                plt.savefig(waterfall_plot_path, dpi=150, bbox_inches='tight')
                plt.close()
                analysis_results['shap_plots_saved'].append(waterfall_plot_path)
                print(f"    ✓ SHAP waterfall plot已保存: {waterfall_plot_path}")

            except Exception as e_waterfall:
                print(f"    ⚠️ 生成waterfall plot时出现错误: {e_waterfall}")
                # 尝试生成简化版本的waterfall plot
                try:
                    plt.figure(figsize=(12, 8))
                    if isinstance(shap_values, list):
                        # 使用第一个类别的SHAP值
                        shap_vals_to_plot = shap_values[1][sample_idx]  # 上涨类别
                        title_suffix = " (上涨类别)"
                    else:
                        shap_vals_to_plot = shap_values[sample_idx]
                        title_suffix = ""

                    # 创建简化的条形图显示特征贡献
                    # 确保shap_vals_to_plot是一维数组
                    if hasattr(shap_vals_to_plot, 'shape') and len(shap_vals_to_plot.shape) > 1:
                        shap_vals_to_plot = shap_vals_to_plot.flatten()

                    feature_contributions = list(zip(feature_names, shap_vals_to_plot))
                    feature_contributions.sort(key=lambda x: abs(float(x[1])), reverse=True)

                    # 只显示前10个最重要的特征
                    top_features = feature_contributions[:10]
                    feat_names = [f[0] for f in top_features]
                    feat_values = [f[1] for f in top_features]

                    colors = ['red' if v < 0 else 'blue' for v in feat_values]
                    y_pos = range(len(feat_names))

                    plt.barh(y_pos, feat_values, color=colors, alpha=0.7)
                    plt.yticks(y_pos, feat_names)
                    plt.xlabel('SHAP值 (特征贡献)')
                    plt.title(f'特征贡献分析 - 样本 {sample_idx+1}{title_suffix}', fontsize=14, fontweight='bold')
                    plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)

                    waterfall_plot_path = os.path.join(shap_plots_dir, "shap_feature_contributions.png")
                    plt.savefig(waterfall_plot_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    analysis_results['shap_plots_saved'].append(waterfall_plot_path)
                    print(f"    ✓ 特征贡献图已保存: {waterfall_plot_path}")

                except Exception as e_backup:
                    print(f"    ⚠️ 生成备用图表也失败: {e_backup}")
                    # 继续执行，不影响整体分析

            # 5.4 生成详细的Dependence Plots
            print("    生成SHAP dependence plots...")
            try:
                if isinstance(shap_values, list) and len(shap_values) >= 2:
                    # 定义要分析的关键特征
                    key_features_to_analyze = [
                        'meta_prob_sum_up_down',
                        'meta_change1_meta_prob_sum_up_down',
                        'meta_change1_meta_prob_diff_up_vs_down',
                        'meta_lag1_oof_proba_BTC_15m_DOWN_pfavorable',
                        'oof_proba_BTC_15m_UP_pfavorable',
                        'oof_proba_BTC_15m_DOWN_pfavorable',
                        'global_pdi',
                        'global_mdi',
                        'global_trend_signal'
                    ]

                    # 找到实际存在的特征
                    available_features = []
                    for feat in key_features_to_analyze:
                        if feat in feature_names:
                            available_features.append(feat)

                    print(f"      将为 {len(available_features)} 个关键特征生成dependence plots")

                    # 为每个关键特征生成Class 0和Class 1的dependence plots
                    for feat_name in available_features:
                        try:
                            feat_idx = feature_names.index(feat_name)

                            # Class 1 ("明确上涨_元") Dependence Plot
                            plt.figure(figsize=(10, 6))
                            interaction_feature = None
                            if feat_name == 'meta_prob_sum_up_down':
                                # 使用meta_prob_diff_up_vs_down作为交互特征
                                if 'meta_prob_diff_up_vs_down' in feature_names:
                                    interaction_feature = feature_names.index('meta_prob_diff_up_vs_down')
                            elif feat_name == 'meta_lag1_oof_proba_BTC_15m_DOWN_pfavorable':
                                # 使用当前DOWN概率作为交互特征
                                if 'oof_proba_BTC_15m_DOWN_pfavorable' in feature_names:
                                    interaction_feature = feature_names.index('oof_proba_BTC_15m_DOWN_pfavorable')

                            shap.dependence_plot(
                                feat_idx,
                                shap_values[1],
                                X_val_df,
                                feature_names=feature_names,
                                interaction_index=interaction_feature,
                                show=False
                            )
                            plt.title(f'Dependence Plot: {feat_name} on 明确上涨_元 (Class 1)', fontsize=12, fontweight='bold')
                            dep_plot_class1_path = os.path.join(shap_plots_dir, f"dependence_{feat_name}_class1_up.png")
                            plt.savefig(dep_plot_class1_path, dpi=150, bbox_inches='tight')
                            plt.close()
                            analysis_results['shap_plots_saved'].append(dep_plot_class1_path)
                            print(f"        ✓ {feat_name} Class 1 dependence plot已保存")

                            # Class 0 ("明确下跌_元") Dependence Plot
                            plt.figure(figsize=(10, 6))
                            shap.dependence_plot(
                                feat_idx,
                                shap_values[0],
                                X_val_df,
                                feature_names=feature_names,
                                interaction_index=interaction_feature,
                                show=False
                            )
                            plt.title(f'Dependence Plot: {feat_name} on 明确下跌_元 (Class 0)', fontsize=12, fontweight='bold')
                            dep_plot_class0_path = os.path.join(shap_plots_dir, f"dependence_{feat_name}_class0_down.png")
                            plt.savefig(dep_plot_class0_path, dpi=150, bbox_inches='tight')
                            plt.close()
                            analysis_results['shap_plots_saved'].append(dep_plot_class0_path)
                            print(f"        ✓ {feat_name} Class 0 dependence plot已保存")

                        except Exception as e_dep_feat:
                            print(f"        ⚠️ 生成 {feat_name} dependence plots时出错: {e_dep_feat}")
                            continue

                else:
                    print("      跳过dependence plots生成 (非多分类模型或类别数不足)")

            except Exception as e_dep_plots:
                print(f"    ⚠️ 生成dependence plots时出现错误: {e_dep_plots}")

        except Exception as e_plot:
            print(f"    ⚠️ 生成SHAP图表时出现错误: {e_plot}")
            # 图表生成失败不影响整体分析

        # 6. 保存分析结果到JSON文件
        try:
            # 转换numpy类型为Python原生类型以便JSON序列化
            json_results = {
                'feature_importance': [(name, float(importance)) for name, importance in feature_importance],
                'top_10_features': [(name, float(importance)) for name, importance in feature_importance[:10]],
                'analysis_summary': analysis_results['analysis_summary'],
                'shap_plots_saved': analysis_results['shap_plots_saved'],
                'analysis_timestamp': pd.Timestamp.now().isoformat()
            }

            shap_json_path = os.path.join(save_dir, "shap_analysis.json")
            with open(shap_json_path, 'w', encoding='utf-8') as f:
                json.dump(json_results, f, indent=2, ensure_ascii=False)

            print(f"  ✅ SHAP分析结果已保存: {shap_json_path}")
            analysis_results['json_saved'] = shap_json_path

        except Exception as e_json:
            print(f"  ⚠️ 保存SHAP分析JSON时出现错误: {e_json}")

        return analysis_results

    except Exception as e:
        print(f"  ❌ SHAP分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc(limit=3)
        return None

def calculate_simulated_profit_meta(y_true_meta_val, y_proba_meta_val,
                                   threshold_up=0.4, threshold_down=0.4,
                                   confidence_gap_up=0.1, confidence_gap_down=0.1,
                                   payout_ratio=0.85, verbose=False):
    """
    计算元模型在给定决策阈值下的模拟交易利润

    参数:
    - y_true_meta_val: 验证集真实标签 (0=下跌, 1=上涨, 2=中性)
    - y_proba_meta_val: 验证集概率预测 [P(下跌), P(上涨), P(中性)]
    - threshold_up: 做多决策阈值
    - threshold_down: 做空决策阈值
    - confidence_gap_up: 做多置信度差值要求
    - confidence_gap_down: 做空置信度差值要求
    - payout_ratio: 盈亏比 (胜利时的收益率)
    - verbose: 是否输出详细信息

    返回:
    - dict: 包含期望收益、交易次数、胜率等指标
    """
    if len(y_true_meta_val) == 0 or len(y_proba_meta_val) == 0:
        return {
            'expected_profit_per_trade': 0.0,
            'total_trades': 0,
            'win_rate': 0.0,
            'up_trades': 0,
            'down_trades': 0,
            'up_wins': 0,
            'down_wins': 0,
            'total_profit': 0.0,
            'risk_adjusted_return': 0.0
        }

    total_trades = 0
    total_wins = 0
    total_profit = 0.0
    up_trades = 0
    down_trades = 0
    up_wins = 0
    down_wins = 0

    for i in range(len(y_true_meta_val)):
        true_label = y_true_meta_val[i]
        probas = y_proba_meta_val[i]

        p_down = probas[0]   # P(明确下跌)
        p_up = probas[1]     # P(明确上涨)
        p_neutral = probas[2] # P(中性)

        # 决策逻辑
        signal = None

        # 检查做多条件
        if (p_up >= threshold_up and
            p_up - p_neutral >= confidence_gap_up):
            signal = "UP"
            up_trades += 1

        # 检查做空条件
        elif (p_down >= threshold_down and
              p_down - p_neutral >= confidence_gap_down):
            signal = "DOWN"
            down_trades += 1

        # 如果有信号，计算盈亏
        if signal:
            total_trades += 1

            # 判断交易结果
            is_win = False
            if signal == "UP" and true_label == 1:  # 预测上涨且实际上涨
                is_win = True
                up_wins += 1
            elif signal == "DOWN" and true_label == 0:  # 预测下跌且实际下跌
                is_win = True
                down_wins += 1

            # 计算盈亏 (假设每次下注1单位)
            if is_win:
                total_profit += payout_ratio  # 盈利
                total_wins += 1
            else:
                total_profit -= 1.0  # 亏损全部本金

    # 计算指标
    win_rate = total_wins / total_trades if total_trades > 0 else 0.0
    expected_profit_per_trade = total_profit / total_trades if total_trades > 0 else 0.0

    # 风险调整收益 (考虑交易频率)
    risk_adjusted_return = expected_profit_per_trade * np.sqrt(total_trades) if total_trades > 0 else 0.0

    if verbose:
        print(f"    模拟交易结果: 总交易={total_trades}, 胜率={win_rate:.3f}, "
              f"期望收益/交易={expected_profit_per_trade:.4f}")
        print(f"    UP交易: {up_trades}次, 胜{up_wins}次 | DOWN交易: {down_trades}次, 胜{down_wins}次")

    # 计算更详细的指标
    up_win_rate = up_wins / up_trades if up_trades > 0 else 0.0
    down_win_rate = down_wins / down_trades if down_trades > 0 else 0.0

    # 计算各方向的精确率、召回率、F1分数
    from sklearn.metrics import precision_recall_fscore_support

    # 为了计算P/R/F1，我们需要构造预测和真实标签
    y_pred_signals = []
    y_true_signals = []

    for i in range(len(y_true_meta_val)):
        true_label = y_true_meta_val[i]
        probas = y_proba_meta_val[i]

        p_down = probas[0]
        p_up = probas[1]
        p_neutral = probas[2]

        # 决策逻辑
        signal = None
        if (p_up >= threshold_up and p_up - p_neutral >= confidence_gap_up):
            signal = 1  # UP
        elif (p_down >= threshold_down and p_down - p_neutral >= confidence_gap_down):
            signal = 0  # DOWN
        else:
            signal = 2  # NO_SIGNAL

        if signal != 2:  # 只记录有信号的情况
            y_pred_signals.append(signal)
            y_true_signals.append(true_label)

    # 计算各类别的P/R/F1
    up_precision = up_recall = up_f1 = 0.0
    down_precision = down_recall = down_f1 = 0.0

    if len(y_pred_signals) > 0:
        try:
            precision, recall, f1, _ = precision_recall_fscore_support(
                y_true_signals, y_pred_signals, labels=[0, 1], average=None, zero_division=0
            )
            if len(precision) >= 2:
                down_precision, up_precision = precision[0], precision[1]
                down_recall, up_recall = recall[0], recall[1]
                down_f1, up_f1 = f1[0], f1[1]
        except:
            pass

    # 计算更多关键性能指标
    total_samples = len(y_true_meta_val)
    trade_frequency = total_trades / total_samples if total_samples > 0 else 0.0

    # 计算夏普比率近似值 (期望收益 / 收益波动率的近似)
    sharpe_ratio = 0.0
    if total_trades > 1 and win_rate > 0 and win_rate < 1:
        # 使用胜率的标准差作为波动率的近似
        volatility_proxy = np.sqrt(win_rate * (1 - win_rate))
        if volatility_proxy > 0:
            sharpe_ratio = expected_profit_per_trade / volatility_proxy

    # 计算最大连续亏损 (模拟)
    max_consecutive_losses = 0
    current_consecutive_losses = 0
    for i in range(len(y_true_meta_val)):
        true_label = y_true_meta_val[i]
        probas = y_proba_meta_val[i]

        p_down = probas[0]
        p_up = probas[1]
        p_neutral = probas[2]

        signal = None
        if (p_up >= threshold_up and p_up - p_neutral >= confidence_gap_up):
            signal = "UP"
        elif (p_down >= threshold_down and p_down - p_neutral >= confidence_gap_down):
            signal = "DOWN"

        if signal:
            is_win = (signal == "UP" and true_label == 1) or (signal == "DOWN" and true_label == 0)
            if is_win:
                current_consecutive_losses = 0
            else:
                current_consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, current_consecutive_losses)

    # 计算盈亏比 (平均盈利/平均亏损)
    profit_loss_ratio = payout_ratio if win_rate > 0 else 0.0  # 简化计算，实际盈利是payout_ratio，亏损是1

    # 计算期望收益的置信区间 (简化版)
    if total_trades > 0:
        profit_std = np.sqrt(win_rate * (payout_ratio ** 2) + (1 - win_rate) * (1 ** 2))
        profit_confidence_interval = 1.96 * profit_std / np.sqrt(total_trades)  # 95%置信区间
    else:
        profit_confidence_interval = 0.0

    # 计算交易平衡度 (UP/DOWN交易的平衡程度)
    trade_balance_score = 0.0
    if up_trades > 0 and down_trades > 0:
        trade_balance_score = min(up_trades, down_trades) / max(up_trades, down_trades)

    return {
        # === 核心盈利指标 ===
        'expected_profit_per_trade': expected_profit_per_trade,  # 平均每笔交易期望收益
        'total_profit': total_profit,                            # 总利润
        'average_profit_per_trade': expected_profit_per_trade,   # 别名，便于Optuna使用

        # === 交易统计 ===
        'total_trades': total_trades,                            # 总交易次数
        'num_trades': total_trades,                              # 别名，便于Optuna使用
        'trade_frequency': trade_frequency,                      # 交易频率 (交易次数/总样本数)
        'up_trades': up_trades,                                  # 做多交易次数
        'down_trades': down_trades,                              # 做空交易次数
        'trade_balance_score': trade_balance_score,              # 交易平衡度 [0,1]

        # === 胜率指标 ===
        'win_rate': win_rate,                                    # 总胜率
        'overall_win_rate': win_rate,                            # 总胜率 (别名，便于Optuna使用)
        'up_wins': up_wins,                                      # 做多胜利次数
        'down_wins': down_wins,                                  # 做空胜利次数
        'up_win_rate': up_win_rate,                              # 做多胜率
        'down_win_rate': down_win_rate,                          # 做空胜率

        # === 风险指标 ===
        'risk_adjusted_return': risk_adjusted_return,            # 风险调整收益
        'sharpe_ratio': sharpe_ratio,                            # 夏普比率近似值
        'max_consecutive_losses': max_consecutive_losses,        # 最大连续亏损次数
        'profit_loss_ratio': profit_loss_ratio,                  # 盈亏比
        'profit_confidence_interval': profit_confidence_interval, # 收益置信区间

        # === 精确率/召回率/F1 ===
        'up_precision': up_precision,                            # 做多精确率
        'up_recall': up_recall,                                  # 做多召回率
        'up_f1': up_f1,                                          # 做多F1分数
        'down_precision': down_precision,                        # 做空精确率
        'down_recall': down_recall,                              # 做空召回率
        'down_f1': down_f1,                                      # 做空F1分数

        # === 综合评分 ===
        'overall_f1': (up_f1 + down_f1) / 2.0,                  # 平均F1分数
        'balanced_accuracy': (up_win_rate + down_win_rate) / 2.0 if up_trades > 0 and down_trades > 0 else win_rate
    }


def _calculate_optimization_objective(result, total_samples=None):
    """
    计算优化目标分数，支持多种优化策略

    参数:
    - result: calculate_simulated_profit_meta 的返回结果
    - total_samples: 总样本数，用于计算交易频率相关指标

    返回:
    - float: 优化目标分数
    """
    # 获取配置的优化策略
    optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'risk_adjusted_return')

    if optimization_strategy == 'expected_profit':
        # 策略1: 纯期望收益最大化
        return result['expected_profit_per_trade']

    elif optimization_strategy == 'risk_adjusted_return':
        # 策略2: 风险调整收益 (期望收益 × √交易次数)
        return result['risk_adjusted_return']

    elif optimization_strategy == 'balanced_profit_frequency':
        # 策略3: 平衡收益和交易频率 (期望收益 × log(1 + 交易次数))
        if result['total_trades'] > 0:
            return result['expected_profit_per_trade'] * np.log(1 + result['total_trades'])
        else:
            return 0.0

    elif optimization_strategy == 'win_rate_weighted':
        # 策略4: 胜率加权的期望收益
        return result['expected_profit_per_trade'] * result['win_rate']

    elif optimization_strategy == 'f1_weighted':
        # 策略5: F1分数加权的期望收益
        avg_f1 = (result['up_f1'] + result['down_f1']) / 2.0
        return result['expected_profit_per_trade'] * avg_f1

    elif optimization_strategy == 'sharpe_like':
        # 策略6: 类似夏普比率 (期望收益 / 胜率标准差的近似)
        if result['win_rate'] > 0 and result['win_rate'] < 1:
            volatility_proxy = np.sqrt(result['win_rate'] * (1 - result['win_rate']))
            return result['expected_profit_per_trade'] / volatility_proxy if volatility_proxy > 0 else 0.0
        else:
            return result['expected_profit_per_trade']

    elif optimization_strategy == 'frequency_weighted_profit':
        # 策略7: 期望收益 × (交易次数/总样本数)^0.3 (新增)
        if total_samples and total_samples > 0 and result['total_trades'] > 0:
            frequency_factor = (result['total_trades'] / total_samples) ** 0.3
            return result['expected_profit_per_trade'] * frequency_factor
        else:
            return result['expected_profit_per_trade']

    elif optimization_strategy == 'trade_efficiency':
        # 策略8: (期望收益 × 交易次数) / 总样本数 (新增)
        if total_samples and total_samples > 0:
            return (result['expected_profit_per_trade'] * result['total_trades']) / total_samples
        else:
            return result['expected_profit_per_trade']

    elif optimization_strategy == 'composite_score':
        # 策略9: 综合评分，平衡收益、频率、胜率 (新增，推荐)
        base_profit = result['expected_profit_per_trade']
        win_rate_bonus = result['win_rate'] * 0.5  # 胜率奖励

        # 交易频率奖励 (适中的交易频率最佳)
        if total_samples and total_samples > 0:
            trade_frequency = result['total_trades'] / total_samples
            # 使用倒U型函数，最优频率在5-15%之间
            optimal_frequency = 0.1
            frequency_penalty = -abs(trade_frequency - optimal_frequency) * 2
        else:
            frequency_penalty = 0

        # 平衡UP/DOWN交易奖励
        if result['up_trades'] > 0 and result['down_trades'] > 0:
            balance_ratio = min(result['up_trades'], result['down_trades']) / max(result['up_trades'], result['down_trades'])
            balance_bonus = balance_ratio * 0.2
        else:
            balance_bonus = 0

        return base_profit + win_rate_bonus + frequency_penalty + balance_bonus

    else:
        # 默认: 风险调整收益
        return result['risk_adjusted_return']


def optimize_meta_decision_thresholds(y_true_meta_val, y_proba_meta_val,
                                    optimization_method='grid_search',
                                    n_trials=100, verbose=True):
    """
    🎯 统一决策阈值优化：基于盈利能力的优化目标

    核心改进：
    - 统一使用 calculate_simulated_profit_meta 函数计算盈利指标
    - 支持多种优化策略（风险调整收益、复合评分等）
    - 网格搜索和Optuna都使用相同的优化目标函数
    - 优化目标直接关联实际交易盈利能力

    参数:
    - y_true_meta_val: 验证集真实标签
    - y_proba_meta_val: 验证集概率预测
    - optimization_method: 优化方法 ('grid_search' 或 'optuna')
    - n_trials: Optuna试验次数 (仅当method='optuna'时使用)
    - verbose: 是否输出详细信息

    返回:
    - dict: 最优阈值组合和性能指标（包含期望收益、胜率、夏普比率等）

    优化策略配置 (META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY):
    - 'risk_adjusted_return': 风险调整收益 (默认)
    - 'composite_score': 综合评分 (推荐)
    - 'expected_profit': 纯期望收益最大化
    - 'balanced_profit_frequency': 平衡收益和交易频率
    """
    if verbose:
        print(f"\n🎯 开始元模型决策阈值优化 (方法: {optimization_method})")
        print(f"   验证集大小: {len(y_true_meta_val)} 样本")
        print(f"   类别分布: {np.bincount(y_true_meta_val)}")

    best_result = {
        'threshold_up': 0.4,
        'threshold_down': 0.4,
        'confidence_gap_up': 0.1,
        'confidence_gap_down': 0.1,
        'expected_profit_per_trade': 0.0,
        'total_trades': 0,
        'win_rate': 0.0,
        'optimization_method': optimization_method
    }

    if optimization_method == 'grid_search':
        best_result = _grid_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, verbose)
    elif optimization_method == 'optuna':
        best_result = _optuna_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, n_trials, verbose)
    else:
        print(f"❌ 未知的优化方法: {optimization_method}")
        return best_result

    if verbose:
        print(f"\n✅ 阈值优化完成!")
        print(f"   最优阈值: UP={best_result['threshold_up']:.3f}, DOWN={best_result['threshold_down']:.3f}")
        print(f"   置信度差值: UP={best_result['confidence_gap_up']:.3f}, DOWN={best_result['confidence_gap_down']:.3f}")
        print(f"   期望收益/交易: {best_result['expected_profit_per_trade']:.4f}")
        print(f"   总交易次数: {best_result['total_trades']}")
        print(f"   胜率: {best_result['win_rate']:.3f}")

    return best_result


def _grid_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, verbose=True):
    """
    网格搜索优化元模型决策阈值 - 基于盈利能力的统一优化版

    🎯 核心改进：
    - 统一使用 calculate_simulated_profit_meta 计算盈利指标
    - 通过 _calculate_optimization_objective 应用配置的优化策略
    - 确保与Optuna搜索使用相同的优化目标
    """

    # 从配置获取搜索范围
    threshold_min = getattr(config, 'META_MODEL_THRESHOLD_MIN', 0.1)
    threshold_max = getattr(config, 'META_MODEL_THRESHOLD_MAX', 0.9)
    confidence_gap_min = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MIN', 0.0)
    confidence_gap_max = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MAX', 0.5)

    # 约束配置
    min_trades = getattr(config, 'META_MODEL_MIN_TRADES_CONSTRAINT', 5)
    min_win_rate = getattr(config, 'META_MODEL_MIN_WIN_RATE_CONSTRAINT', 0.4)

    # 定义大幅扩展的搜索空间 (更细的步长以充分探索)
    threshold_range = np.arange(threshold_min, threshold_max + 0.01, 0.02)  # 步长0.02
    confidence_gap_range = np.arange(confidence_gap_min, confidence_gap_max + 0.01, 0.02)  # 步长0.02

    best_profit = -float('inf')
    best_params = None
    total_combinations = len(threshold_range) ** 2 * len(confidence_gap_range) ** 2
    total_samples = len(y_true_meta_val)

    if verbose:
        print(f"   🔍 增强网格搜索空间: {total_combinations:,} 种组合")
        print(f"   📊 扩展搜索范围:")
        print(f"      - 阈值: [{threshold_min:.1f}, {threshold_max:.1f}] (步长: 0.02)")
        print(f"      - 置信度差值: [{confidence_gap_min:.1f}, {confidence_gap_max:.1f}] (步长: 0.02)")
        print(f"   🔒 约束: 最小交易数≥{min_trades}, 最小胜率≥{min_win_rate:.1f}")

    tested_combinations = 0

    for threshold_up in threshold_range:
        for threshold_down in threshold_range:
            for confidence_gap_up in confidence_gap_range:
                for confidence_gap_down in confidence_gap_range:

                    result = calculate_simulated_profit_meta(
                        y_true_meta_val, y_proba_meta_val,
                        threshold_up=threshold_up,
                        threshold_down=threshold_down,
                        confidence_gap_up=confidence_gap_up,
                        confidence_gap_down=confidence_gap_down,
                        verbose=False
                    )

                    # 强化约束检查
                    if result['total_trades'] < min_trades or result['win_rate'] < min_win_rate:
                        tested_combinations += 1
                        continue  # 跳过不满足约束的组合

                    # 使用增强的优化目标选择 (传入总样本数)
                    objective_score = _calculate_optimization_objective(result, total_samples)

                    # 更新最佳结果
                    if objective_score > best_profit:
                        best_profit = objective_score
                        best_params = {
                            'threshold_up': threshold_up,
                            'threshold_down': threshold_down,
                            'confidence_gap_up': confidence_gap_up,
                            'confidence_gap_down': confidence_gap_down,
                            **result,
                            'optimization_method': 'grid_search',
                            'objective_score': objective_score
                        }

                    tested_combinations += 1

                    if verbose and tested_combinations % 500 == 0:
                        print(f"   已测试 {tested_combinations}/{total_combinations} 组合...")

    if best_params is None:
        # 如果没有找到满足最小交易次数的组合，降低约束
        if verbose:
            print("   ⚠️ 未找到满足最小交易次数(5)的组合，降低约束重新搜索...")

        best_profit = -float('inf')
        for threshold_up in threshold_range:
            for threshold_down in threshold_range:
                for confidence_gap_up in confidence_gap_range:
                    for confidence_gap_down in confidence_gap_range:

                        result = calculate_simulated_profit_meta(
                            y_true_meta_val, y_proba_meta_val,
                            threshold_up=threshold_up,
                            threshold_down=threshold_down,
                            confidence_gap_up=confidence_gap_up,
                            confidence_gap_down=confidence_gap_down,
                            verbose=False
                        )

                        # 🎯 统一使用基于盈利能力的优化目标 (即使在降级搜索中)
                        objective_score = _calculate_optimization_objective(result, total_samples)

                        if result['total_trades'] >= 1 and objective_score > best_profit:
                            best_profit = objective_score
                            best_params = {
                                'threshold_up': threshold_up,
                                'threshold_down': threshold_down,
                                'confidence_gap_up': confidence_gap_up,
                                'confidence_gap_down': confidence_gap_down,
                                **result,
                                'optimization_method': 'grid_search',
                                'objective_score': objective_score
                            }

    return best_params if best_params else {
        'threshold_up': 0.4, 'threshold_down': 0.4,
        'confidence_gap_up': 0.1, 'confidence_gap_down': 0.1,
        'expected_profit_per_trade': 0.0, 'total_trades': 0, 'win_rate': 0.0,
        'optimization_method': 'grid_search'
    }


def _optuna_search_meta_thresholds(y_true_meta_val, y_proba_meta_val, n_trials=100, verbose=True):
    """
    使用Optuna优化元模型决策阈值 - 基于盈利能力的统一优化版

    🎯 核心改进：
    - 目标函数直接调用 calculate_simulated_profit_meta 计算盈利指标
    - 支持多种优化策略（通过配置选择）
    - 强制约束优化，确保交易质量
    - 与网格搜索使用相同的优化目标逻辑
    """

    # 从配置获取搜索范围
    threshold_min = getattr(config, 'META_MODEL_THRESHOLD_MIN', 0.1)
    threshold_max = getattr(config, 'META_MODEL_THRESHOLD_MAX', 0.9)
    confidence_gap_min = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MIN', 0.0)
    confidence_gap_max = getattr(config, 'META_MODEL_CONFIDENCE_GAP_MAX', 0.5)

    # 约束配置
    min_trades = getattr(config, 'META_MODEL_MIN_TRADES_CONSTRAINT', 5)
    min_win_rate = getattr(config, 'META_MODEL_MIN_WIN_RATE_CONSTRAINT', 0.4)
    enable_early_stopping = getattr(config, 'META_MODEL_ENABLE_EARLY_STOPPING', True)
    timeout = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_TIMEOUT', 3600)
    early_stop_patience = getattr(config, 'META_MODEL_OPTUNA_EARLY_STOP_PATIENCE', 30)
    n_startup_trials = getattr(config, 'META_MODEL_OPTUNA_SAMPLER_N_STARTUP_TRIALS', 50)

    # 获取优化策略
    optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'composite_score')

    # 计算验证集大小用于约束计算
    total_samples = len(y_true_meta_val)

    if verbose:
        print(f"   🎯 Optuna增强优化版: {n_trials} 次试验 (超时: {timeout//60}分钟)")
        print(f"   📊 优化搜索空间:")
        print(f"      - 阈值范围: [{threshold_min:.1f}, {threshold_max:.1f}]")
        print(f"      - 置信度差值: [{confidence_gap_min:.1f}, {confidence_gap_max:.1f}] (降低最大值以探索更小GAP)")
        print(f"   🔒 动态约束优化:")
        print(f"      - 验证集大小: {total_samples}")
        print(f"      - 最小交易数: 基于验证集长度动态计算 (至少{max(20, int(total_samples * 0.01))}次)")
        print(f"      - 最小胜率: 动态范围 [0.20, 0.50]")
        print(f"   🎯 优化策略: {optimization_strategy}")
        print(f"   ⚡ 早停设置: {early_stop_patience}次无改进停止, 前{n_startup_trials}次随机探索")

    total_samples = len(y_true_meta_val)
    best_score_so_far = -float('inf')
    trials_without_improvement = 0

    def objective(trial):
        """
        🎯 彻底革命性的Optuna目标函数 - 强制约束优化引擎

        核心改革：
        1. 让Optuna也优化约束参数本身
        2. 强制执行硬约束，不满足直接返回固定惩罚值
        3. 多样化优化目标，包括复杂组合指标
        4. 增强负数惩罚机制
        """
        nonlocal best_score_so_far, trials_without_improvement

        # === 🚀 革命性改进1: 让Optuna也优化约束参数 ===
        # 🎯 强制执行更合理的最小交易次数约束
        # 计算验证集长度的百分比作为最小交易次数
        validation_length = len(y_true_meta_val)
        min_trades_percentage = max(20, int(validation_length * 0.01))  # 至少20次或验证集的1%
        MIN_TRADES_REQUIRED = trial.suggest_int("min_trades_constraint", min_trades_percentage, min_trades_percentage + 10)

        # 动态优化最小胜率约束 (更宽松的范围)
        MIN_WIN_RATE_REQUIRED = trial.suggest_float("min_win_rate_constraint", 0.20, 0.50)

        # 动态优化最大连续亏损约束
        MAX_CONSECUTIVE_LOSSES_ALLOWED = trial.suggest_int("max_consecutive_losses_constraint", 10, 30)

        # === 🚀 革命性改进2: 扩大搜索空间，特别是Confidence Gap ===
        # 🎯 扩大Confidence Gap搜索范围，使用更小的步长以探索更精细的值
        confidence_gap_up = trial.suggest_float('confidence_gap_up', confidence_gap_min, confidence_gap_max, step=0.01)
        confidence_gap_down = trial.suggest_float('confidence_gap_down', confidence_gap_min, confidence_gap_max, step=0.01)

        # 🎯 扩大决策阈值搜索范围，使用更小的步长
        threshold_up = trial.suggest_float('threshold_up', threshold_min, threshold_max, step=0.02)
        threshold_down = trial.suggest_float('threshold_down', threshold_min, threshold_max, step=0.02)

        # === 计算所有性能指标 ===
        metrics = calculate_simulated_profit_meta(
            y_true_meta_val, y_proba_meta_val,
            threshold_up=threshold_up,
            threshold_down=threshold_down,
            confidence_gap_up=confidence_gap_up,
            confidence_gap_down=confidence_gap_down,
            verbose=False
        )

        # === 🚀 革命性改进2: 强制执行硬约束，返回固定惩罚值 ===

        # 硬约束1: 最小交易次数
        if metrics['num_trades'] < MIN_TRADES_REQUIRED:
            return -1000.0  # 返回固定的严重惩罚值

        # 硬约束2: 最小胜率
        if metrics['win_rate'] < MIN_WIN_RATE_REQUIRED:
            return -1000.0  # 返回固定的严重惩罚值

        # 硬约束3: 最大连续亏损限制
        if metrics['max_consecutive_losses'] > MAX_CONSECUTIVE_LOSSES_ALLOWED:
            return -1000.0  # 返回固定的严重惩罚值

        # 硬约束4: 交易平衡性 (避免过度偏向单一方向)
        if metrics['num_trades'] >= 10:
            if metrics['up_trades'] == 0 or metrics['down_trades'] == 0:
                return -1000.0  # 返回固定的严重惩罚值

        # === 🚀 革命性改进3: 多样化优化目标实验 ===

        # 基础指标
        average_profit_per_trade = metrics['average_profit_per_trade']
        num_trades = metrics['num_trades']
        win_rate = metrics['win_rate']
        total_profit = metrics.get('total_profit', average_profit_per_trade * num_trades)

        # 🎯 目标1: 总盈利最大化 (隐式鼓励更多正收益交易)
        objective_1_total_profit = total_profit

        # 🎯 目标2: 简化的风险调整收益 (收益 × √交易次数)
        objective_2_risk_adjusted = average_profit_per_trade * np.sqrt(num_trades)

        # 🎯 目标3: 复杂效用函数 - 正收益时鼓励交易频率，负收益时重惩罚
        if average_profit_per_trade > 0:
            # 正收益时：收益与交易次数正相关
            objective_3_utility = average_profit_per_trade * (1 + np.log(max(1, num_trades)) * 0.1)
        else:
            # 负收益时：重惩罚，惩罚程度与交易次数成正比
            objective_3_utility = average_profit_per_trade * (1 + num_trades * 0.1)

        # 🎯 目标4: 风险调整收益 (考虑夏普比率)
        sharpe_ratio = metrics.get('sharpe_ratio', 0.0)
        objective_4_risk_adjusted_sharpe = average_profit_per_trade * (1 + sharpe_ratio * 0.1)

        # 🎯 目标5: 平衡收益、频率和胜率的综合指标
        objective_5_comprehensive = (
            total_profit * 0.4 +                       # 40% 总收益权重
            average_profit_per_trade * np.sqrt(num_trades) * 0.4 +  # 40% 风险调整收益
            win_rate * num_trades * 0.2                 # 20% 胜率×频率权重
        )

        # === 🚀 革命性改进4: 增强负数惩罚机制 ===

        # 如果平均收益为负，返回更大的负数惩罚
        if average_profit_per_trade < 0:
            # 负收益的惩罚程度与亏损幅度成正比
            negative_penalty = -500.0 * abs(average_profit_per_trade)
            return max(negative_penalty, -2000.0)  # 限制最大惩罚值

        # === 选择最终优化目标 ===
        optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'composite_score')

        if optimization_strategy == 'profit_first':
            # 策略1: 优先最大化总收益
            final_objective = objective_1_total_profit

        elif optimization_strategy == 'balanced_profit_frequency':
            # 策略2: 平衡收益和频率 (风险调整收益)
            final_objective = objective_2_risk_adjusted

        elif optimization_strategy == 'frequency_under_winrate_constraint':
            # 策略3: 胜率约束下最大化交易频率
            if win_rate > 0.55:
                final_objective = num_trades + average_profit_per_trade * 10
            else:
                final_objective = average_profit_per_trade

        elif optimization_strategy == 'composite_score':
            # 策略4: 综合评分 (推荐) - 使用复杂效用函数
            final_objective = objective_3_utility

        elif optimization_strategy == 'risk_adjusted':
            # 策略5: 风险调整收益 (考虑夏普比率)
            final_objective = objective_4_risk_adjusted_sharpe

        elif optimization_strategy == 'total_expected_profit':
            # 策略6: 综合指标 (总收益+风险调整+胜率)
            final_objective = objective_5_comprehensive

        else:
            # 默认: 总收益优先
            final_objective = objective_1_total_profit

        # === 额外奖励机制 (保持原有逻辑) ===
        bonus_score = 0.0

        # 奖励1: 高胜率奖励 (分级)
        if win_rate > 0.75:
            bonus_score += 0.5 * (win_rate - 0.75)
        elif win_rate > 0.65:
            bonus_score += 0.3 * (win_rate - 0.65)
        elif win_rate > 0.55:
            bonus_score += 0.1 * (win_rate - 0.55)

        # 奖励2: 交易平衡奖励
        trade_balance_score = metrics.get('trade_balance_score', 0.0)
        if trade_balance_score > 0.7:
            bonus_score += 0.2 * trade_balance_score

        # 奖励3: 低风险奖励 (连续亏损少)
        if metrics['max_consecutive_losses'] <= 3:
            bonus_score += 0.1
        elif metrics['max_consecutive_losses'] <= 5:
            bonus_score += 0.05

        # 奖励4: 夏普比率奖励
        if sharpe_ratio > 1.0:
            bonus_score += 0.1 * (sharpe_ratio - 1.0)

        # === 计算最终分数 ===
        final_score = final_objective + bonus_score

        # === 早停机制 ===
        if enable_early_stopping:
            if final_score > best_score_so_far:
                best_score_so_far = final_score
                trials_without_improvement = 0
            else:
                trials_without_improvement += 1

            if trials_without_improvement >= early_stop_patience:
                trial.study.stop()

        # === 记录关键指标到trial ===
        trial.set_user_attr('profit_per_trade', average_profit_per_trade)
        trial.set_user_attr('total_trades', num_trades)
        trial.set_user_attr('win_rate', win_rate)
        trial.set_user_attr('total_profit', total_profit)
        trial.set_user_attr('trade_frequency', metrics.get('trade_frequency', 0.0))
        trial.set_user_attr('max_consecutive_losses', metrics['max_consecutive_losses'])
        trial.set_user_attr('sharpe_ratio', sharpe_ratio)
        trial.set_user_attr('min_trades_constraint', MIN_TRADES_REQUIRED)
        trial.set_user_attr('min_win_rate_constraint', MIN_WIN_RATE_REQUIRED)
        trial.set_user_attr('max_consecutive_losses_constraint', MAX_CONSECUTIVE_LOSSES_ALLOWED)

        # 记录各个优化目标的值用于分析
        trial.set_user_attr('objective_1_total_profit', objective_1_total_profit)
        trial.set_user_attr('objective_2_risk_adjusted', objective_2_risk_adjusted)
        trial.set_user_attr('objective_3_utility', objective_3_utility)
        trial.set_user_attr('objective_4_risk_adjusted_sharpe', objective_4_risk_adjusted_sharpe)
        trial.set_user_attr('objective_5_comprehensive', objective_5_comprehensive)
        trial.set_user_attr('optimization_strategy', optimization_strategy)
        trial.set_user_attr('final_score', final_score)

        return final_score

    try:
        # 创建彻底增强的Optuna study
        optuna_direction = getattr(config, 'META_MODEL_OPTUNA_DIRECTION', 'maximize')
        study = optuna.create_study(
            direction=optuna_direction,
            sampler=optuna.samplers.TPESampler(
                seed=42,
                n_startup_trials=min(n_startup_trials, n_trials // 4),  # 前25%试验用于随机探索
                n_ei_candidates=32,  # 增加候选点数量以更好探索
                multivariate=True,   # 启用多变量优化
                group=True,          # 启用参数分组优化
                warn_independent_sampling=False  # 减少警告信息
            ),
            pruner=optuna.pruners.MedianPruner(
                n_startup_trials=min(15, n_trials // 10),  # 前10%试验不剪枝
                n_warmup_steps=8,    # 增加预热步数
                interval_steps=1
            )
        )

        # 运行优化 (增加超时保护)
        study.optimize(
            objective,
            n_trials=n_trials,
            timeout=timeout,
            show_progress_bar=verbose
        )

        # 获取最佳参数
        best_params = study.best_params

        # 计算最佳参数的详细结果
        best_result = calculate_simulated_profit_meta(
            y_true_meta_val, y_proba_meta_val,
            threshold_up=best_params['threshold_up'],
            threshold_down=best_params['threshold_down'],
            confidence_gap_up=best_params['confidence_gap_up'],
            confidence_gap_down=best_params['confidence_gap_down'],
            verbose=False
        )

        # 合并参数和结果
        best_result.update(best_params)
        best_result['optimization_method'] = 'optuna'
        best_result['best_value'] = study.best_value
        best_result['n_trials'] = len(study.trials)

        if verbose:
            print(f"   Optuna最佳值: {study.best_value:.4f}")
            print(f"   完成试验: {len(study.trials)}/{n_trials}")

        return best_result

    except Exception as e:
        if verbose:
            print(f"   ⚠️ Optuna优化失败: {e}")

        # 返回默认值
        return {
            'threshold_up': 0.4, 'threshold_down': 0.4,
            'confidence_gap_up': 0.1, 'confidence_gap_down': 0.1,
            'expected_profit_per_trade': 0.0, 'total_trades': 0, 'win_rate': 0.0,
            'optimization_method': 'optuna_failed'
        }


def load_optimal_meta_thresholds():
    """
    加载优化后的元模型决策阈值

    返回:
    - dict: 包含最优阈值的字典，如果加载失败则返回默认值
    """
    default_thresholds = {
        'threshold_up': getattr(config, 'META_SIGNAL_UP_THRESHOLD', 0.4),
        'threshold_down': getattr(config, 'META_SIGNAL_DOWN_THRESHOLD', 0.4),
        'confidence_gap_up': getattr(config, 'META_SIGNAL_CONFIDENCE_GAP_UP', 0.1),
        'confidence_gap_down': getattr(config, 'META_SIGNAL_CONFIDENCE_GAP_DOWN', 0.1)
    }

    # 尝试从保存的文件加载
    meta_model_dir = getattr(config, 'META_MODEL_SAVE_DIR', "meta_model_data")
    threshold_file_path = os.path.join(meta_model_dir, "optimal_thresholds.json")

    if os.path.exists(threshold_file_path):
        try:
            with open(threshold_file_path, 'r', encoding='utf-8') as f:
                optimal_thresholds = json.load(f)

            # 验证加载的阈值是否有效
            required_keys = ['threshold_up', 'threshold_down', 'confidence_gap_up', 'confidence_gap_down']
            if all(key in optimal_thresholds for key in required_keys):
                print(f"  [MetaThresholds] 成功加载优化阈值: {threshold_file_path}")
                print(f"    UP阈值: {optimal_thresholds['threshold_up']:.3f}, DOWN阈值: {optimal_thresholds['threshold_down']:.3f}")
                print(f"    UP置信度差值: {optimal_thresholds['confidence_gap_up']:.3f}, DOWN置信度差值: {optimal_thresholds['confidence_gap_down']:.3f}")
                return optimal_thresholds
            else:
                print(f"  ⚠️ [MetaThresholds] 优化阈值文件格式不完整，使用默认值")

        except Exception as e:
            print(f"  ⚠️ [MetaThresholds] 加载优化阈值失败: {e}，使用默认值")
    else:
        print(f"  [MetaThresholds] 优化阈值文件不存在，使用默认值: {threshold_file_path}")

    return default_thresholds


def calculate_optuna_meta_metric(metric_name, y_true, y_pred_proba, num_classes, verbose_metric_calc=False):
    """
    为元模型的Optuna计算指定的评估指标。
    🎯 新增：支持基于模拟交易盈利能力的优化目标

    参数:
    - metric_name: 指标名称
    - y_true: 真实标签
    - y_pred_proba: 预测概率 (predict_proba的输出)
    - num_classes: 类别数量
    - verbose_metric_calc: 是否输出详细计算信息
    """
    # 确保导入必要的函数
    from sklearn.metrics import f1_score, accuracy_score, precision_score, recall_score, log_loss

    y_pred_labels = np.argmax(y_pred_proba, axis=1) # 从概率中获取预测类别

    # 🎯 新增：基于盈利能力的优化指标
    if metric_name == 'simulated_profit_expected':
        """期望收益优化 - 直接优化模拟交易的期望收益"""
        try:
            # 使用默认的决策阈值进行模拟交易
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )
            return profit_result['expected_profit_per_trade']
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算模拟交易期望收益时出错: {e}")
            return -1.0  # 返回负值表示失败

    elif metric_name == 'simulated_profit_risk_adjusted':
        """风险调整收益优化 - 考虑交易频率的风险调整收益"""
        try:
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )
            return profit_result['risk_adjusted_return']
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算风险调整收益时出错: {e}")
            return -1.0

    elif metric_name == 'simulated_profit_composite':
        """复合盈利指标 - 综合考虑收益、胜率和交易频率"""
        try:
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )

            # 复合评分：期望收益 × 胜率 × sqrt(交易频率)
            expected_profit = profit_result['expected_profit_per_trade']
            win_rate = profit_result['win_rate']
            trade_frequency = profit_result['trade_frequency']

            # 添加约束：最小交易次数和胜率
            if profit_result['num_trades'] < 10 or win_rate < 0.3:
                return -1.0  # 不满足基本约束

            composite_score = expected_profit * win_rate * np.sqrt(trade_frequency)
            return composite_score
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算复合盈利指标时出错: {e}")
            return -1.0

    elif metric_name == 'simulated_profit_sharpe':
        """夏普比率优化 - 基于模拟交易的夏普比率"""
        try:
            profit_result = calculate_simulated_profit_meta(
                y_true, y_pred_proba,
                threshold_up=0.4, threshold_down=0.4,
                confidence_gap_up=0.1, confidence_gap_down=0.1,
                verbose=verbose_metric_calc
            )

            # 使用夏普比率，但添加最小交易次数约束
            if profit_result['num_trades'] < 10:
                return -1.0

            return profit_result.get('sharpe_ratio', -1.0)
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算夏普比率时出错: {e}")
            return -1.0

    # 🎯 保留原有的技术指标（向后兼容）
    elif metric_name == 'macro_f1_score':
        return f1_score(y_true, y_pred_labels, average='macro', zero_division=0)
    elif metric_name == 'weighted_f1_score':
        return f1_score(y_true, y_pred_labels, average='weighted', zero_division=0)
    elif metric_name == 'val_accuracy': # 之前元模型用的
        return accuracy_score(y_true, y_pred_labels)
    elif metric_name == 'multi_logloss':
        try:
            return -log_loss(y_true, y_pred_proba)  # 负号使其变为最大化目标
        except Exception:
            return -10.0  # 返回较大的负值表示失败

    elif metric_name == 'custom_f1_class01_avg':
        """自定义F1指标：Class 0和1的F1分数平均值"""
        try:
            # 计算Class 0和1的F1分数
            f1_scores = f1_score(y_true, y_pred_labels, average=None, zero_division=0)
            if len(f1_scores) >= 2:
                # 只取Class 0和1的F1分数
                f1_class_0 = f1_scores[0]
                f1_class_1 = f1_scores[1]
                avg_f1_class01 = (f1_class_0 + f1_class_1) / 2.0
                return avg_f1_class01
            else:
                return 0.0
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算custom_f1_class01_avg时出错: {e}")
            return 0.0

    elif metric_name == 'custom_precision_class01_avg':
        """自定义精确率指标：Class 0和1的精确率平均值"""
        try:
            precision_scores = precision_score(y_true, y_pred_labels, average=None, zero_division=0)
            if len(precision_scores) >= 2:
                precision_class_0 = precision_scores[0]
                precision_class_1 = precision_scores[1]
                avg_precision_class01 = (precision_class_0 + precision_class_1) / 2.0
                return avg_precision_class01
            else:
                return 0.0
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算custom_precision_class01_avg时出错: {e}")
            return 0.0

    elif metric_name == 'custom_recall_class01_avg':
        """自定义召回率指标：Class 0和1的召回率平均值"""
        try:
            recall_scores = recall_score(y_true, y_pred_labels, average=None, zero_division=0)
            if len(recall_scores) >= 2:
                recall_class_0 = recall_scores[0]
                recall_class_1 = recall_scores[1]
                avg_recall_class01 = (recall_class_0 + recall_class_1) / 2.0
                return avg_recall_class01
            else:
                return 0.0
        except Exception as e:
            if verbose_metric_calc:
                print(f"计算custom_recall_class01_avg时出错: {e}")
            return 0.0

    else:
        # 未知指标，返回默认值
        if verbose_metric_calc:
            print(f"未知的优化指标: {metric_name}")
        return 0.0




def train_meta_model(X_meta_df_input, y_meta_series_input):
    print("\n>>> prediction.train_meta_model 函数已开始执行...")

    # 获取保存目录配置并创建目录 (在函数开始就定义，确保整个函数都可以使用)
    meta_model_save_dir_final = getattr(config, 'META_MODEL_SAVE_DIR', "meta_model_data")
    print(f"  prediction.train_meta_model: 配置的保存目录: {meta_model_save_dir_final}")

    # 确保使用绝对路径
    if not os.path.isabs(meta_model_save_dir_final):
        meta_model_save_dir_final = os.path.abspath(meta_model_save_dir_final)
    print(f"  prediction.train_meta_model: 绝对保存路径: {meta_model_save_dir_final}")

    try:
        os.makedirs(meta_model_save_dir_final, exist_ok=True)
        print(f"  prediction.train_meta_model: 保存目录创建成功: {meta_model_save_dir_final}")
    except Exception as e_mkdir:
        print(f"!!! prediction.train_meta_model: 创建保存目录失败: {e_mkdir}")
        return False, None, None, None, {"status": f'Error_CreateDir: {str(e_mkdir)}'}

    if not isinstance(X_meta_df_input, pd.DataFrame) or not isinstance(y_meta_series_input, pd.Series):
        print("!!! prediction.train_meta_model: 输入的 X_meta 或 y_meta 类型不正确。")
        return False, None, None, None, {"status": "Error_InputType"}
    if X_meta_df_input.empty or y_meta_series_input.empty:
        print("!!! prediction.train_meta_model: 传入的X_meta或y_meta为空。")
        return False, None, None, None, {"status": "Error_InputEmpty"}
    
    common_idx_train_meta_internal = X_meta_df_input.index.intersection(y_meta_series_input.index)
    if len(common_idx_train_meta_internal) < len(X_meta_df_input) * 0.95:
        print(f"!!! prediction.train_meta_model: X_meta和y_meta索引差异过大或不匹配，中止。 X_len={len(X_meta_df_input)}, Common_len={len(common_idx_train_meta_internal)}")
        return False, None, None, None, {"status": "Error_IndexMismatch"}
    
    # 移除不必要的.copy()操作，减少内存消耗
    # loc操作已经创建了一个新的视图，不需要额外的copy
    X_meta_df = X_meta_df_input.loc[common_idx_train_meta_internal]
    y_meta_series = y_meta_series_input.loc[common_idx_train_meta_internal]
    
    if X_meta_df.empty or y_meta_series.empty:
        print("!!! prediction.train_meta_model: 对齐后X_meta或y_meta为空，中止。")
        return False, None, None, None, {"status": "Error_InputEmptyAfterAlign"}

    print(f"  prediction.train_meta_model: 元模型训练数据形状 (对齐后): X_meta={X_meta_df.shape}, y_meta={y_meta_series.shape}")
    print(f"  prediction.train_meta_model: 元模型目标变量分布 (y_meta):\n{y_meta_series.value_counts(normalize=True).sort_index()}")

    # --- Optuna 相关配置 ---
    optuna_enabled_meta = getattr(config, 'META_MODEL_OPTUNA_ENABLE', False)
    best_params_from_optuna = {}

    if optuna_enabled_meta:
        print("  prediction.train_meta_model: Optuna超参数优化已为元模型启用。")
        
        def objective_meta(trial):
            # 定义参数搜索空间
            param_grid_meta = getattr(config, 'META_MODEL_OPTUNA_PARAM_GRID', {})
            params = {}
            for name, (param_type, low, high, *args) in param_grid_meta.items():
                if param_type == 'int':
                    params[name] = trial.suggest_int(name, low, high)
                elif param_type == 'float':
                    log_scale = args[0] if args else False
                    params[name] = trial.suggest_float(name, low, high, log=log_scale)
                elif param_type == 'categorical':
                    params[name] = trial.suggest_categorical(name, high) # 'high' here is the list of categories

            # 固定参数
            params['objective'] = getattr(config, 'META_MODEL_LGBM_OBJECTIVE', 'multiclass')
            params['num_class'] = getattr(config, 'META_MODEL_LGBM_NUM_CLASS', 3)
            params['metric'] = getattr(config, 'META_MODEL_OPTUNA_LGBM_EVAL_METRIC', 'multi_logloss') # 早停用此指标
            params['n_estimators'] = getattr(config, 'META_MODEL_OPTUNA_LGBM_N_ESTIMATORS_MAX', 300)
            params['random_state'] = getattr(config, 'META_MODEL_LGBM_RANDOM_STATE', 2024) + trial.number # 确保每次试验种子不同
            params['n_jobs'] = -1
            params['device_type'] = getattr(config, 'META_MODEL_LGBM_DEVICE', 'cpu').lower()
            params['class_weight'] = getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', 'balanced') # 通常固定
            params['verbosity'] = -1 # Optuna试验时保持静默

            # 清理参数，只保留LGBM认识的
            lgbm_trial_params_clean = {k: v for k, v in params.items() if k in VALID_LGBM_PARAM_KEYS_PRED_PY}

            cv_scores = []
            # 使用 TimeSeriesSplit 进行交叉验证
            n_splits_optuna_cv = getattr(config, 'META_MODEL_OPTUNA_CV_FOLDS', 3)
            if n_splits_optuna_cv <= 1: # 如果CV折数不合理，则不进行CV，直接在全数据上评估（或划分一次验证集）
                print(f"  Optuna Trial {trial.number}: CV folds ({n_splits_optuna_cv}) <= 1, will use single validation split for this trial.")
                 # 可以选择划分一个固定的验证集来评估，或者如果数据量小就直接在训练集上评估（不推荐）
                # 这里我们简化，如果CV折数不合理，就返回一个很差的值，促使配置被修正
                return -float('inf') if getattr(config, 'META_MODEL_OPTUNA_DIRECTION', "maximize") == "maximize" else float('inf')


            tscv_optuna = TimeSeriesSplit(n_splits=n_splits_optuna_cv)
            
            # 获取早停回调
            early_stopping_rounds_optuna = getattr(config, 'META_MODEL_OPTUNA_LGBM_EARLY_STOPPING_ROUNDS', 30)
            callbacks_optuna_trial = [early_stopping(early_stopping_rounds_optuna, verbose=False, first_metric_only=False)] if early_stopping_rounds_optuna > 0 else None

            for fold_idx, (train_indices, val_indices) in enumerate(tscv_optuna.split(X_meta_df.values, y_meta_series.values)):
                X_train_fold_np, X_val_fold_np = X_meta_df.values[train_indices], X_meta_df.values[val_indices]
                y_train_fold_np, y_val_fold_np = y_meta_series.values[train_indices], y_meta_series.values[val_indices]

                if len(np.unique(y_val_fold_np)) < params['num_class']: # 验证集类别不足
                    print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: Validation set has only {len(np.unique(y_val_fold_np))} classes. Skipping fold.")
                    cv_scores.append(-1.0) # 或者一个非常差的值
                    continue

                # 应用SMOTE过采样 (仅在训练数据上)
                smote_enabled_optuna = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and getattr(config, 'META_MODEL_SMOTE_ENABLE', True)
                if smote_enabled_optuna:
                    unique_labels_optuna, counts_optuna = np.unique(y_train_fold_np, return_counts=True)
                    minority_class_count_optuna = counts_optuna.min()
                    smote_min_threshold_optuna = getattr(config, 'META_MODEL_SMOTE_MIN_SAMPLES_THRESHOLD', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

                    if minority_class_count_optuna < smote_min_threshold_optuna:
                        print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: 少数类样本 ({minority_class_count_optuna}) 过少，跳过SMOTE。")
                        X_train_fold_resampled_np = X_train_fold_np
                        y_train_fold_resampled_np = y_train_fold_np
                    else:
                        try:
                            print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: 应用SMOTE前类别分布: {dict(zip(unique_labels_optuna, counts_optuna))}")
                            # k_neighbors 的值不能超过少数类样本数 - 1
                            smote_k_neighbors_config_optuna = getattr(config, 'META_MODEL_SMOTE_K_NEIGHBORS', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                            smote_k_neighbors_optuna = min(smote_k_neighbors_config_optuna, minority_class_count_optuna - 1) if minority_class_count_optuna > 1 else 1
                            smote_random_state_optuna = getattr(config, 'META_MODEL_SMOTE_RANDOM_STATE', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                            from imblearn.over_sampling import SMOTE  # 确保导入SMOTE
                            sm_optuna = SMOTE(random_state=smote_random_state_optuna, k_neighbors=smote_k_neighbors_optuna)
                            X_train_fold_resampled_np, y_train_fold_resampled_np = sm_optuna.fit_resample(X_train_fold_np, y_train_fold_np)
                            unique_labels_after_optuna, counts_after_optuna = np.unique(y_train_fold_resampled_np, return_counts=True)
                            print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: 应用SMOTE后类别分布: {dict(zip(unique_labels_after_optuna, counts_after_optuna))}")
                        except ValueError as e_smote_optuna:
                            print(f"    !!! Optuna Trial {trial.number}, Fold {fold_idx}: SMOTE执行失败: {e_smote_optuna}。将使用原始训练数据。")
                            X_train_fold_resampled_np = X_train_fold_np
                            y_train_fold_resampled_np = y_train_fold_np
                else:
                    print(f"    Optuna Trial {trial.number}, Fold {fold_idx}: SMOTE已禁用，使用原始训练数据。")
                    X_train_fold_resampled_np = X_train_fold_np
                    y_train_fold_resampled_np = y_train_fold_np

                model_trial = LGBMClassifier(**lgbm_trial_params_clean)
                try:
                    model_trial.fit(X_train_fold_resampled_np, y_train_fold_resampled_np,
                                    eval_set=[(X_val_fold_np, y_val_fold_np)],
                                    eval_metric=params['metric'], # 使用LGBM早停的指标
                                    callbacks=callbacks_optuna_trial,
                                    feature_name=list(X_meta_df.columns))
                except Exception as e_fit_trial:
                    print(f"    !!! Optuna Trial {trial.number}, Fold {fold_idx} fit error: {e_fit_trial}")
                    cv_scores.append(-1.0) # 或者一个非常差的值
                    continue

                y_pred_proba_val_fold = model_trial.predict_proba(X_val_fold_np)
                
                # 使用我们定义的 calculate_optuna_meta_metric 和 config 中配置的优化目标指标
                optuna_target_metric_name = getattr(config, 'META_MODEL_OPTUNA_METRIC', 'macro_f1_score')
                score_fold = calculate_optuna_meta_metric(optuna_target_metric_name, y_val_fold_np, y_pred_proba_val_fold, params['num_class'])
                cv_scores.append(score_fold)
                
                # Optuna剪枝（可选）
                trial.report(np.mean(cv_scores) if cv_scores else 0.0, fold_idx)
                if trial.should_prune():
                    raise optuna.exceptions.TrialPruned()
            
            return np.mean(cv_scores) if cv_scores else 0.0 # 返回平均CV分数

        # --- 运行 Optuna study ---
        optuna_direction = getattr(config, 'META_MODEL_OPTUNA_DIRECTION', "maximize")
        study = optuna.create_study(direction=optuna_direction)
        try:
            study.optimize(objective_meta,
                           n_trials=getattr(config, 'META_MODEL_OPTUNA_N_TRIALS', 50),
                           timeout=getattr(config, 'META_MODEL_OPTUNA_TIMEOUT', None),
                           n_jobs=1, # LightGBM自身可以多线程，Optuna的n_jobs通常设为1
                           show_progress_bar=True) # 显示进度条
            best_params_from_optuna = study.best_params
            # 🚨 修复：暂存Optuna结果，稍后输出
            optuna_best_value = study.best_value
            optuna_best_params = best_params_from_optuna
        except optuna.exceptions.TrialPruned as e_pruned:
            print(f"  prediction.train_meta_model: Optuna试验被剪枝: {e_pruned}")
        except Exception as e_optuna_run:
            print(f"  !!! prediction.train_meta_model: Optuna优化过程中发生错误: {e_optuna_run}")
            traceback.print_exc(limit=1)
            best_params_from_optuna = {} # 出错则不使用优化参数
    
    # --- 准备最终的LGBM参数 ---
    if optuna_enabled_meta and best_params_from_optuna:
        # 🚨 修复：暂存消息，稍后输出
        optuna_success_message = "将使用Optuna找到的最佳参数进行元模型最终训练。"
        # 从Optuna的最佳参数开始，并补充固定的和必要的参数
        final_lgbm_params = best_params_from_optuna.copy()
        final_lgbm_params['objective'] = getattr(config, 'META_MODEL_LGBM_OBJECTIVE', 'multiclass')
        final_lgbm_params['num_class'] = getattr(config, 'META_MODEL_LGBM_NUM_CLASS', 3)
        final_lgbm_params['metric'] = getattr(config, 'META_MODEL_LGBM_METRIC', 'multi_logloss') # 最终训练和评估用此指标
        final_lgbm_params['n_estimators'] = getattr(config, 'META_MODEL_LGBM_N_ESTIMATORS', 100) # 可以用config中的值或Optuna也优化它
        final_lgbm_params['random_state'] = getattr(config, 'META_MODEL_LGBM_RANDOM_STATE', 2024)
        final_lgbm_params['n_jobs'] = -1
        final_lgbm_params['device_type'] = getattr(config, 'META_MODEL_LGBM_DEVICE', 'cpu').lower()
        final_lgbm_params['class_weight'] = getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', 'balanced')
        final_lgbm_params['verbosity'] = getattr(config, 'META_MODEL_LGBM_VERBOSE', -1) # 最终训练时的verbose
    else:
        if optuna_enabled_meta: # Optuna启用了但没有找到最佳参数（例如出错或所有试验都剪枝了）
            print("  prediction.train_meta_model: Optuna未找到有效参数，将使用config.py中的固定参数进行元模型训练。")
        else:
            print("  prediction.train_meta_model: Optuna未启用，将使用config.py中的固定参数进行元模型训练。")
        # 使用 config.py 中的固定参数
        fixed_params = {
            'objective': getattr(config, 'META_MODEL_LGBM_OBJECTIVE', 'multiclass'),
            'num_class': getattr(config, 'META_MODEL_LGBM_NUM_CLASS', 3),
            'metric': getattr(config, 'META_MODEL_LGBM_METRIC', 'multi_logloss'),
            'n_estimators': getattr(config, 'META_MODEL_LGBM_N_ESTIMATORS', 100),
            'learning_rate': getattr(config, 'META_MODEL_LGBM_LEARNING_RATE', 0.02),
            'num_leaves': getattr(config, 'META_MODEL_LGBM_NUM_LEAVES', 10),
            'max_depth': getattr(config, 'META_MODEL_LGBM_MAX_DEPTH', 4),
            'reg_alpha': getattr(config, 'META_MODEL_LGBM_REG_ALPHA', 2.0),
            'reg_lambda': getattr(config, 'META_MODEL_LGBM_REG_LAMBDA', 2.0),
            'colsample_bytree': getattr(config, 'META_MODEL_LGBM_COLSAMPLE_BYTREE', 0.7),
            'subsample': getattr(config, 'META_MODEL_LGBM_SUBSAMPLE', 0.7),
            'min_child_samples': getattr(config, 'META_MODEL_LGBM_MIN_CHILD_SAMPLES', 30),
            'random_state': getattr(config, 'META_MODEL_LGBM_RANDOM_STATE', 2024),
            'verbose': getattr(config, 'META_MODEL_LGBM_VERBOSE', -1),
            'n_jobs': -1,
            'device_type': getattr(config, 'META_MODEL_LGBM_DEVICE', 'cpu').lower(),
            'class_weight': getattr(config, 'META_MODEL_LGBM_CLASS_WEIGHT', 'balanced')
        }
        final_lgbm_params = fixed_params

    # 清理最终参数并设置verbosity
    meta_lgbm_final_params_to_use = {k: v for k, v in final_lgbm_params.items() if k in VALID_LGBM_PARAM_KEYS_PRED_PY}
    if 'verbose' in meta_lgbm_final_params_to_use:
        lgbm_verbosity_level = meta_lgbm_final_params_to_use.pop('verbose')
        if lgbm_verbosity_level == 0: meta_lgbm_final_params_to_use['verbosity'] = -1
        elif lgbm_verbosity_level == 1: meta_lgbm_final_params_to_use['verbosity'] = 0
        elif lgbm_verbosity_level > 1: meta_lgbm_final_params_to_use['verbosity'] = 1
        else: meta_lgbm_final_params_to_use['verbosity'] = lgbm_verbosity_level

    # 🚨 修复：暂存参数信息，稍后输出
    final_params_info = json.dumps(meta_lgbm_final_params_to_use, indent=2)

    # --- 后续的模型训练、评估、保存逻辑与之前类似，使用 meta_lgbm_final_params_to_use ---
    trained_model_object_to_return = None
    X_val_data_to_return_np = None 
    y_val_data_to_return_np = None 
    evaluation_results_to_return_dict = {"status": "评估未开始或无验证数据"}

    X_meta_np_for_split = X_meta_df.values
    y_meta_np_for_split = y_meta_series.values
    
    X_train_meta_for_fit, y_train_meta_for_fit = X_meta_np_for_split, y_meta_np_for_split
    eval_set_for_meta_fit = None
    callbacks_for_meta_fit = None
    
    eval_ratio_meta_config = getattr(config, 'META_MODEL_TRAIN_TEST_SPLIT_FOR_EVAL_RATIO', 0.2)
    if 0 < eval_ratio_meta_config < 1:
        # ... (之前的数据划分逻辑，用于最终训练和评估) ...
        num_samples_meta_total = len(X_meta_np_for_split)
        num_val_samples_meta_calc = int(num_samples_meta_total * eval_ratio_meta_config)
        min_val_samples_meta_config = getattr(config, 'META_MODEL_OOF_MIN_SAMPLES_FOR_EARLY_STOP_VALID', 30) # 复用这个配置
        if num_val_samples_meta_calc >= min_val_samples_meta_config and (num_samples_meta_total - num_val_samples_meta_calc) >= min_val_samples_meta_config:
            train_size_meta_split = num_samples_meta_total - num_val_samples_meta_calc
            X_train_meta_for_fit, y_train_meta_for_fit = X_meta_np_for_split[:train_size_meta_split], y_meta_np_for_split[:train_size_meta_split]
            X_val_data_to_return_np, y_val_data_to_return_np = X_meta_np_for_split[train_size_meta_split:], y_meta_np_for_split[train_size_meta_split:]
            if len(np.unique(y_val_data_to_return_np)) < meta_lgbm_final_params_to_use.get('num_class', 3):
                 print(f"  prediction.train_meta_model: 警告 - 元模型验证集类别数不足。不使用早停，使用全部数据训练。")
                 X_train_meta_for_fit, y_train_meta_for_fit = X_meta_np_for_split, y_meta_np_for_split
                 X_val_data_to_return_np, y_val_data_to_return_np = None, None
            else:
                eval_set_for_meta_fit = [(X_val_data_to_return_np, y_val_data_to_return_np)]
                final_esr_meta = getattr(config, 'META_MODEL_LGBM_EARLY_STOPPING_ROUNDS_FINAL', 20)
                if final_esr_meta > 0 :
                    callbacks_for_meta_fit = [
                        early_stopping(
                            stopping_rounds=final_esr_meta,
                            verbose=meta_lgbm_final_params_to_use.get('verbosity',0) > 0,
                            first_metric_only=False
                        )
                    ]
            print(f"  prediction.train_meta_model: 元模型数据划分: 训练集 {len(X_train_meta_for_fit)} 条, 验证集 {len(X_val_data_to_return_np) if X_val_data_to_return_np is not None else 0} 条。")
        else:
            print(f"  prediction.train_meta_model: 警告 - 验证/训练集样本不足，不划分，使用全部数据训练。")
            X_val_data_to_return_np, y_val_data_to_return_np = None, None
    else:
        print(f"  prediction.train_meta_model: 验证集划分比例无效，使用全部数据训练。")
        X_val_data_to_return_np, y_val_data_to_return_np = None, None

    # 应用SMOTE过采样 (仅在训练数据上)
    smote_enabled_meta = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and getattr(config, 'META_MODEL_SMOTE_ENABLE', True)
    if smote_enabled_meta:
        unique_labels_meta, counts_meta = np.unique(y_train_meta_for_fit, return_counts=True)
        minority_class_count_meta = counts_meta.min()
        smote_min_threshold_meta = getattr(config, 'META_MODEL_SMOTE_MIN_SAMPLES_THRESHOLD', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

        if minority_class_count_meta < smote_min_threshold_meta:
            print(f"  prediction.train_meta_model: 少数类样本 ({minority_class_count_meta}) 过少，跳过SMOTE。")
            X_train_meta_resampled = X_train_meta_for_fit
            y_train_meta_resampled = y_train_meta_for_fit
        else:
            try:
                print(f"  prediction.train_meta_model: 应用SMOTE前类别分布: {dict(zip(unique_labels_meta, counts_meta))}")
                # k_neighbors 的值不能超过少数类样本数 - 1
                smote_k_neighbors_config_meta = getattr(config, 'META_MODEL_SMOTE_K_NEIGHBORS', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                smote_k_neighbors_meta = min(smote_k_neighbors_config_meta, minority_class_count_meta - 1) if minority_class_count_meta > 1 else 1
                smote_random_state_meta = getattr(config, 'META_MODEL_SMOTE_RANDOM_STATE', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                from imblearn.over_sampling import SMOTE  # 确保导入SMOTE
                sm_meta = SMOTE(random_state=smote_random_state_meta, k_neighbors=smote_k_neighbors_meta)
                X_train_meta_resampled, y_train_meta_resampled = sm_meta.fit_resample(X_train_meta_for_fit, y_train_meta_for_fit)
                unique_labels_after_meta, counts_after_meta = np.unique(y_train_meta_resampled, return_counts=True)
                print(f"  prediction.train_meta_model: 应用SMOTE后类别分布: {dict(zip(unique_labels_after_meta, counts_after_meta))}")
            except ValueError as e_smote_meta:
                print(f"  !!! prediction.train_meta_model: SMOTE执行失败: {e_smote_meta}。将使用原始训练数据。")
                X_train_meta_resampled = X_train_meta_for_fit
                y_train_meta_resampled = y_train_meta_for_fit
    else:
        print(f"  prediction.train_meta_model: SMOTE已禁用，使用原始训练数据。")
        X_train_meta_resampled = X_train_meta_for_fit
        y_train_meta_resampled = y_train_meta_for_fit

    trained_model_object_to_return = LGBMClassifier(**meta_lgbm_final_params_to_use)
    try:
        print("  prediction.train_meta_model: 开始拟合元模型...")
        trained_model_object_to_return.fit(
            X_train_meta_resampled, y_train_meta_resampled,
            eval_set=eval_set_for_meta_fit,
            eval_metric=meta_lgbm_final_params_to_use.get('metric'), # 使用最终训练的评估指标
            callbacks=callbacks_for_meta_fit,
            feature_name=list(X_meta_df.columns)
        )
        print("  prediction.train_meta_model: 元模型拟合完成。")
        if eval_set_for_meta_fit and hasattr(trained_model_object_to_return, 'best_iteration_') and trained_model_object_to_return.best_iteration_ is not None:
            print(f"  prediction.train_meta_model: 元模型早停于第 {trained_model_object_to_return.best_iteration_} 轮。")
            evaluation_results_to_return_dict['best_iteration'] = trained_model_object_to_return.best_iteration_
            # ... (获取 best_score_ 的逻辑)
    except Exception as e_fit_meta_in_func_final:
        print(f"!!! prediction.train_meta_model: 元模型训练失败: {e_fit_meta_in_func_final}")
        traceback.print_exc(limit=2)
        evaluation_results_to_return_dict['status'] = f'Error_Fit: {str(e_fit_meta_in_func_final)}'
        return False, None, X_val_data_to_return_np, y_val_data_to_return_np, evaluation_results_to_return_dict

    # --- 评估部分 ---
    if X_val_data_to_return_np is not None and y_val_data_to_return_np is not None and len(y_val_data_to_return_np) > 0:
        print("\n  prediction.train_meta_model: --- 元模型在内部验证集上的评估 ---")
        try:
            # 直接使用NumPy数组进行预测，避免创建不必要的DataFrame
            # 这样可以减少内存消耗，因为LightGBM可以直接处理NumPy数组
            y_pred_meta_val_final_eval = trained_model_object_to_return.predict(X_val_data_to_return_np)
            
            # 如果后续代码需要使用DataFrame，再创建一次
            X_meta_val_df_for_pred_final_eval = pd.DataFrame(X_val_data_to_return_np, columns=list(X_meta_df.columns))
            # 直接使用NumPy数组进行概率预测，避免使用DataFrame
            y_proba_meta_val_final_eval = trained_model_object_to_return.predict_proba(X_val_data_to_return_np)

            evaluation_results_to_return_dict['val_accuracy'] = accuracy_score(y_val_data_to_return_np, y_pred_meta_val_final_eval)
            evaluation_results_to_return_dict['val_logloss'] = log_loss(y_val_data_to_return_np, y_proba_meta_val_final_eval, labels=trained_model_object_to_return.classes_)
            
            target_names_for_report_final = [f"Class_{c}" for c in trained_model_object_to_return.classes_] if hasattr(trained_model_object_to_return, 'classes_') else ['Down_Meta(0)', 'Up_Meta(1)', 'Neutral_Meta(2)']
            labels_for_report_final = trained_model_object_to_return.classes_ if hasattr(trained_model_object_to_return, 'classes_') else [0,1,2]

            evaluation_results_to_return_dict['val_classification_report_dict'] = classification_report(
                y_val_data_to_return_np, y_pred_meta_val_final_eval, output_dict=True, zero_division=0,
                labels=labels_for_report_final, target_names=target_names_for_report_final
            )
            evaluation_results_to_return_dict['status'] = "评估完成"
            # 🚨 修复：暂存评估结果，稍后输出
            evaluation_accuracy = evaluation_results_to_return_dict['val_accuracy']
            evaluation_logloss = evaluation_results_to_return_dict['val_logloss']
            evaluation_report = classification_report(y_val_data_to_return_np, y_pred_meta_val_final_eval, digits=3, zero_division=0,
                                        labels=labels_for_report_final, target_names=target_names_for_report_final)

            # --- 元模型决策阈值优化 (重新启用) ---
            # 🎯 重新启用阈值优化，在已优化的概率质量基础上进一步提升交易性能
            print("  📋 元模型阈值优化已重新启用")
            print("  🎯 目标：在优化的概率质量基础上找到更好的决策边界")

            # 计算并显示详细的三分类性能指标
            try:
                from sklearn.metrics import precision_recall_fscore_support, confusion_matrix

                # 计算每个类别的精确率、召回率、F1分数
                precision, recall, f1, support = precision_recall_fscore_support(
                    y_val_data_to_return_np, y_pred_meta_val_final_eval,
                    labels=[0, 1, 2], zero_division=0
                )

                # 计算混淆矩阵
                cm = confusion_matrix(y_val_data_to_return_np, y_pred_meta_val_final_eval, labels=[0, 1, 2])

                print("\n  📊 详细三分类性能分析 (使用argmax决策):")
                print("  " + "="*50)
                for i, class_name in enumerate(['Class 0 (下跌)', 'Class 1 (上涨)', 'Class 2 (中性)']):
                    print(f"  {class_name}:")
                    print(f"    精确率 (Precision): {precision[i]:.4f}")
                    print(f"    召回率 (Recall):    {recall[i]:.4f}")
                    print(f"    F1分数:            {f1[i]:.4f}")
                    print(f"    支持样本数:        {support[i]}")
                    print()

                # 重点关注Class 0和1的性能
                class01_precision_avg = (precision[0] + precision[1]) / 2
                class01_recall_avg = (recall[0] + recall[1]) / 2
                class01_f1_avg = (f1[0] + f1[1]) / 2

                print(f"  🎯 Class 0&1 平均性能 (交易相关类别):")
                print(f"    平均精确率: {class01_precision_avg:.4f}")
                print(f"    平均召回率: {class01_recall_avg:.4f}")
                print(f"    平均F1分数: {class01_f1_avg:.4f}")
                print()

                print(f"  📈 混淆矩阵:")
                print(f"    实际\\预测  Class0  Class1  Class2")
                for i, row in enumerate(cm):
                    print(f"    Class{i}     {row[0]:6d}  {row[1]:6d}  {row[2]:6d}")
                print("  " + "="*50)

                # 保存详细性能到评估结果
                evaluation_results_to_return_dict['detailed_class_performance'] = {
                    'precision': precision.tolist(),
                    'recall': recall.tolist(),
                    'f1': f1.tolist(),
                    'support': support.tolist(),
                    'class01_avg_precision': class01_precision_avg,
                    'class01_avg_recall': class01_recall_avg,
                    'class01_avg_f1': class01_f1_avg,
                    'confusion_matrix': cm.tolist()
                }

            except Exception as e_detailed_eval:
                print(f"  ⚠️ 详细性能分析出错: {e_detailed_eval}")

            try:
                # 🚨 修复：暂存阈值优化开始消息，稍后输出
                threshold_optimization_start_message = "--- 开始元模型决策阈值优化 ---"

                # 检查是否启用阈值优化
                enable_threshold_optimization = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_ENABLE', True)
                optimization_method = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_METHOD', 'optuna')
                n_trials = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_N_TRIALS', 500)
                optimization_strategy = getattr(config, 'META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY', 'composite_score')

                if enable_threshold_optimization and len(y_val_data_to_return_np) >= 10:
                    # 🚨 修复：暂存阈值优化方法消息，稍后输出
                    threshold_optimization_method_message = f"元模型阈值优化已启用 (方法: {optimization_method}, 策略: {optimization_strategy}, 试验数: {n_trials})"

                    optimal_thresholds = optimize_meta_decision_thresholds(
                        y_val_data_to_return_np,
                        y_proba_meta_val_final_eval,
                        optimization_method=optimization_method,
                        n_trials=n_trials,
                        verbose=True
                    )

                    # 保存优化结果到评估字典
                    evaluation_results_to_return_dict['optimal_thresholds'] = optimal_thresholds

                    # 保存到配置文件或JSON
                    threshold_save_path = os.path.join(meta_model_save_dir_final, "optimal_thresholds.json")
                    try:
                        with open(threshold_save_path, 'w', encoding='utf-8') as f_thresh:
                            json.dump(optimal_thresholds, f_thresh, indent=2, ensure_ascii=False)
                        print(f"  ✅ 最优阈值已保存到: {threshold_save_path}")
                    except Exception as e_save_thresh:
                        print(f"  ⚠️ 保存最优阈值失败: {e_save_thresh}")

                    # 更新config.py中的阈值 (可选)
                    try:
                        print(f"  💡 建议更新config.py中的元模型决策阈值:")
                        print(f"     META_SIGNAL_UP_THRESHOLD = {optimal_thresholds['threshold_up']:.3f}")
                        print(f"     META_SIGNAL_DOWN_THRESHOLD = {optimal_thresholds['threshold_down']:.3f}")
                        if 'confidence_gap_up' in optimal_thresholds:
                            print(f"     META_SIGNAL_CONFIDENCE_GAP_UP = {optimal_thresholds['confidence_gap_up']:.3f}")
                        if 'confidence_gap_down' in optimal_thresholds:
                            print(f"     META_SIGNAL_CONFIDENCE_GAP_DOWN = {optimal_thresholds['confidence_gap_down']:.3f}")
                        if 'confidence_gap' in optimal_thresholds:
                            print(f"     META_SIGNAL_CONFIDENCE_GAP = {optimal_thresholds['confidence_gap']:.3f}")
                    except:
                        pass

                else:
                    if not enable_threshold_optimization:
                        print(f"  元模型阈值优化已禁用")
                    else:
                        print(f"  验证集样本不足 ({len(y_val_data_to_return_np)} < 10)，跳过阈值优化")

            except Exception as e_threshold_opt:
                print(f"  ⚠️ 元模型阈值优化过程中出现错误: {e_threshold_opt}")
                print("  阈值优化失败不会影响元模型训练的成功")
                import traceback
                traceback.print_exc(limit=2)
        except Exception as e_eval_meta_in_func_final:
            print(f"  !!! prediction.train_meta_model: 评估元模型时出错: {e_eval_meta_in_func_final}")
            traceback.print_exc(limit=1)
            evaluation_results_to_return_dict['status'] = f'Error_Eval: {str(e_eval_meta_in_func_final)}'
    else:
        print("  prediction.train_meta_model: 未划分验证集或验证集为空，跳过元模型内部评估。")
        evaluation_results_to_return_dict['status'] = 'NoValData_For_InternalEval'
        evaluation_results_to_return_dict['val_accuracy'] = None
        evaluation_results_to_return_dict['val_logloss'] = None
        evaluation_results_to_return_dict['val_classification_report_dict'] = None

    # 构建文件路径
    meta_model_filename_to_save = META_MODEL_FILENAME
    meta_model_filepath_to_save = os.path.join(meta_model_save_dir_final, meta_model_filename_to_save)
    meta_features_filename_to_save = META_FEATURES_FILENAME
    meta_features_filepath_to_save = os.path.join(meta_model_save_dir_final, meta_features_filename_to_save)
    meta_params_filename_to_save = "meta_model_params.json"
    meta_params_filepath_to_save = os.path.join(meta_model_save_dir_final, meta_params_filename_to_save)

    print(f"  prediction.train_meta_model: 准备保存文件:")
    print(f"    模型文件: {meta_model_filepath_to_save}")
    print(f"    特征文件: {meta_features_filepath_to_save}")
    print(f"    参数文件: {meta_params_filepath_to_save}")

    try:
        # 保存模型文件
        print(f"  prediction.train_meta_model: 开始保存模型文件...")
        joblib.dump(trained_model_object_to_return, meta_model_filepath_to_save, compress=3)
        if os.path.exists(meta_model_filepath_to_save):
            file_size = os.path.getsize(meta_model_filepath_to_save)
            print(f"  ✅ 元模型已成功保存到: {meta_model_filepath_to_save} (大小: {file_size} 字节)")
        else:
            print(f"  ❌ 元模型保存失败，文件不存在: {meta_model_filepath_to_save}")

        # 保存特征列表
        print(f"  prediction.train_meta_model: 开始保存特征列表...")
        with open(meta_features_filepath_to_save, 'w', encoding='utf-8') as f_json_meta_feat_write:
            json.dump(list(X_meta_df.columns), f_json_meta_feat_write, indent=2, ensure_ascii=False)
        if os.path.exists(meta_features_filepath_to_save):
            print(f"  ✅ 特征列表已成功保存到: {meta_features_filepath_to_save}")
        else:
            print(f"  ❌ 特征列表保存失败，文件不存在: {meta_features_filepath_to_save}")

        # 保存模型参数
        print(f"  prediction.train_meta_model: 开始保存模型参数...")
        with open(meta_params_filepath_to_save, 'w', encoding='utf-8') as f_params_meta_write:
            json.dump(trained_model_object_to_return.get_params(), f_params_meta_write, indent=4, ensure_ascii=False)
        if os.path.exists(meta_params_filepath_to_save):
            print(f"  ✅ 模型参数已成功保存到: {meta_params_filepath_to_save}")
        else:
            print(f"  ❌ 模型参数保存失败，文件不存在: {meta_params_filepath_to_save}")

    except Exception as e_save_meta_model_in_func_final:
        print(f"!!! prediction.train_meta_model: 保存元模型或其元数据失败: {e_save_meta_model_in_func_final}")
        import traceback
        traceback.print_exc()
        evaluation_results_to_return_dict['status'] = f'Error_Save: {str(e_save_meta_model_in_func_final)}'
        return False, trained_model_object_to_return, X_val_data_to_return_np, y_val_data_to_return_np, evaluation_results_to_return_dict

    # --- SHAP可解释性分析 ---
    try:
        print("\n  prediction.train_meta_model: --- 开始SHAP可解释性分析 ---")
        shap_analysis_results = perform_shap_analysis(
            trained_model_object_to_return,
            X_val_data_to_return_np,
            y_val_data_to_return_np,
            list(X_meta_df.columns),
            meta_model_save_dir_final
        )
        if shap_analysis_results:
            evaluation_results_to_return_dict['shap_analysis'] = shap_analysis_results
            print("  ✅ SHAP可解释性分析完成")
        else:
            print("  ⚠️ SHAP分析未成功完成，但不影响模型训练")
    except Exception as e_shap:
        print(f"  ⚠️ SHAP分析过程中出现错误: {e_shap}")
        print("  SHAP分析失败不会影响元模型训练的成功")
        import traceback
        traceback.print_exc(limit=2)

    # 🚨 修复：在最后输出所有暂存的详细信息
    print("\n" + "="*60)
    print("📊 元模型训练详细信息")
    print("="*60)

    # 输出Optuna优化结果
    if 'optuna_best_value' in locals() and 'optuna_best_params' in locals():
        print(f"prediction.train_meta_model: Optuna优化完成。最佳试验值 ({getattr(config, 'META_MODEL_OPTUNA_METRIC', 'macro_f1_score')}): {optuna_best_value:.4f}")
        print(f"prediction.train_meta_model: Optuna找到的最佳参数: {optuna_best_params}")

    # 输出参数使用信息
    if 'optuna_success_message' in locals():
        print(f"prediction.train_meta_model: {optuna_success_message}")

    # 输出最终参数
    if 'final_params_info' in locals():
        print(f"prediction.train_meta_model: 元模型最终LightGBM参数: {final_params_info}")

    # 输出评估结果
    if 'evaluation_accuracy' in locals() and 'evaluation_logloss' in locals() and 'evaluation_report' in locals():
        print(f"验证集准确率 (Accuracy): {evaluation_accuracy:.4f}")
        print(f"验证集LogLoss: {evaluation_logloss:.4f}")
        print(f"验证集分类报告:")
        print(evaluation_report)

    # 输出阈值优化信息 (重新启用)
    if 'threshold_optimization_start_message' in locals():
        print(f"\nprediction.train_meta_model: {threshold_optimization_start_message}")
    if 'threshold_optimization_method_message' in locals():
        print(f"{threshold_optimization_method_message}")

    # 输出阈值优化结果
    if 'optimal_thresholds' in evaluation_results_to_return_dict:
        optimal_thresholds = evaluation_results_to_return_dict['optimal_thresholds']
        print(f"\n🎯 元模型阈值优化结果:")
        print(f"   最优上涨阈值: {optimal_thresholds.get('threshold_up', 'N/A')}")
        print(f"   最优下跌阈值: {optimal_thresholds.get('threshold_down', 'N/A')}")
        if 'confidence_gap' in optimal_thresholds:
            print(f"   置信度差值: {optimal_thresholds['confidence_gap']}")
        if 'expected_profit' in optimal_thresholds:
            print(f"   预期收益: {optimal_thresholds['expected_profit']:.4f}")
        if 'num_trades' in optimal_thresholds:
            print(f"   交易次数: {optimal_thresholds['num_trades']}")
        if 'win_rate' in optimal_thresholds:
            print(f"   胜率: {optimal_thresholds['win_rate']:.2%}")
    else:
        print(f"\nprediction.train_meta_model: 阈值优化未执行或失败")

    print("="*60)
    print("<---<<< prediction.train_meta_model 函数执行结束。")
    return True, trained_model_object_to_return, X_val_data_to_return_np, y_val_data_to_return_np, evaluation_results_to_return_dict


# --- DynamicConfigManager 初始化 ---
DYNAMIC_CONFIG_FILEPATH = "dynamic_params.json"

def get_global_dynamic_config_manager():
    return dynamic_config_manager

def load_meta_model_if_needed():
    """加载元模型和其特征名列表 (如果尚未加载)。"""
    # global meta_model_instance, meta_model_feature_names, meta_model_loaded_successfully
    
    if global_prediction_state_manager.is_meta_model_loaded_successfully():
        return True

    if not getattr(config, 'ENABLE_META_MODEL_TRAINING', False) and not getattr(config, 'ENABLE_META_MODEL_PREDICTION', True): # 新增一个开关控制是否使用元模型进行预测
        print("  [MetaModel Load] 元模型预测未启用 (config.ENABLE_META_MODEL_PREDICTION = False)。")
        return False

    meta_model_dir = getattr(config, 'META_MODEL_SAVE_DIR', "trained_models/meta_model_data") # 从config获取
    
    meta_model_path = os.path.join(meta_model_dir, META_MODEL_FILENAME)
    meta_features_path = os.path.join(meta_model_dir, META_FEATURES_FILENAME)

    if not os.path.exists(meta_model_path):
        print(f"!!! [MetaModel Load] 元模型文件未找到: {meta_model_path}")
        global_prediction_state_manager.set_meta_model_loaded_successfully(False)
        return False
    if not os.path.exists(meta_features_path):
        print(f"!!! [MetaModel Load] 元模型特征列表文件未找到: {meta_features_path}")
        global_prediction_state_manager.set_meta_model_loaded_successfully(False)
        return False

    try:
        loaded_model_instance = joblib.load(meta_model_path)
        with open(meta_features_path, 'r') as f:
            loaded_feature_names = json.load(f)
        
        if loaded_model_instance and loaded_feature_names:
            global_prediction_state_manager.set_meta_model_instance(loaded_model_instance)
            global_prediction_state_manager.set_meta_model_feature_names(loaded_feature_names)
            global_prediction_state_manager.set_meta_model_loaded_successfully(True)
            print(f"  [MetaModel Load] 元模型 '{META_MODEL_FILENAME}' 和特征列表 ({len(loaded_feature_names)} feats) 加载成功。")
            return True
        else:
            print(f"!!! [MetaModel Load] 元模型或特征列表加载后为空。")
            global_prediction_state_manager.set_meta_model_instance(None)
            global_prediction_state_manager.set_meta_model_feature_names(None)
            global_prediction_state_manager.set_meta_model_loaded_successfully(False)
            return False
    except Exception as e:
        print(f"!!! [MetaModel Load] 加载元模型或特征列表时发生错误: {e}")
        global_prediction_state_manager.set_meta_model_instance(None)
        global_prediction_state_manager.set_meta_model_feature_names(None)
        global_prediction_state_manager.set_meta_model_loaded_successfully(False)
        return False


def _get_default_dynamic_config_from_static():
    initial_dynamic_config = {"targets": {}, "global_settings": {"master_signal_sending_enabled": True}}
    if hasattr(config, 'PREDICTION_TARGETS') and isinstance(config.PREDICTION_TARGETS, list):
        for static_cfg_item in config.PREDICTION_TARGETS:
            if isinstance(static_cfg_item, dict) and 'name' in static_cfg_item:
                target_name = static_cfg_item['name']
                static_kelly_params = static_cfg_item.get('kelly_config_params', {})
                initial_dynamic_config["targets"][target_name] = {
                    "virtual_total_capital_for_kelly": static_kelly_params.get("virtual_total_capital_for_kelly", 1000.0),
                    "win_rate_p_estimate": static_kelly_params.get("win_rate_p_estimate", 0.55),
                    "max_kelly_fraction_f": static_kelly_params.get("max_kelly_fraction_f", 0.1),
                    "enabled": True 
                }
    return initial_dynamic_config

try:
    dynamic_config_manager = DynamicConfigManager(DYNAMIC_CONFIG_FILEPATH, _get_default_dynamic_config_from_static)
except Exception as e_dcm_init:
    print(f"!!! CRITICAL ERROR during DynamicConfigManager instantiation in prediction.py: {e_dcm_init}")
    traceback.print_exc()

def stop_dynamic_config_observer_on_exit(): # 重命名以避免与atexit.register冲突
    if 'dynamic_config_manager' in globals() and dynamic_config_manager:
        dynamic_config_manager.stop_observer()
atexit.register(stop_dynamic_config_observer_on_exit)


def get_prediction_lock():
    return prediction_lock

# --- 通知与声音函数 (保持不变) ---
def _notify_simulator(signal_type, target_name_origin, amount=None, symbol_for_sim=None, sim_url_param=None):
    if not SEND_SIGNALS_TO_SIMULATOR: # 使用模块级的 SEND_SIGNALS_TO_SIMULATOR
        # print(f"[{target_name_origin}] 模拟盘信号发送已禁用 (全局配置)。")
        return False
    
    actual_sim_url = sim_url_param
    if not actual_sim_url: 
        actual_sim_url = SIMULATOR_API_URL # 使用模块级的默认 URL
        if not actual_sim_url:
            print(f"!!! 预测系统: 未能确定模拟盘URL，不发送信号 for {target_name_origin}。")
            return False
        # print(f"警告: _notify_simulator 未收到特定 sim_url, 将使用模块级配置的 URL: {actual_sim_url}")

    payload = {
        "signal_type": signal_type.upper(),
        "target_name": str(target_name_origin) 
    }
    if amount is not None: 
        payload["amount"] = float(amount)
    if symbol_for_sim: 
        payload["symbol"] = str(symbol_for_sim).upper() 
    
    # print(f"DEBUG prediction.py/_notify_simulator: Sending payload: {payload} to URL: {actual_sim_url}") 
    
    try:
        response = requests.post(actual_sim_url, json=payload, timeout=3)
        response.raise_for_status()
        log_symbol = symbol_for_sim if symbol_for_sim else "UnknownSymbol"
        print(f"预测系统 [{log_symbol} via {target_name_origin}]: 信号成功发送至模拟盘 ({actual_sim_url})...")
        return True
    except Exception as e: 
        log_symbol = symbol_for_sim if symbol_for_sim else "UnknownSymbol"
        print(f"!!! 预测系统 [{log_symbol} via {target_name_origin}]: 发送信号至模拟盘 ({actual_sim_url}) 出错: {e}"); 
        return False

def _stop_music_after_duration():
    """辅助函数，用于在定时器触发时停止音乐（或开始淡出）"""
    # music_fadeout_timer is now managed by PredictionStateManager
    if pygame.mixer.music.get_busy():
        fadeout_duration = 1000 # 例如，用1秒时间淡出
        print(f"  [Pygame Sound] 开始淡出音乐 ({fadeout_duration}ms)...")
        pygame.mixer.music.fadeout(fadeout_duration)
    
    current_timer = global_prediction_state_manager.get_music_fadeout_timer()
    if current_timer:
        current_timer.cancel() # 取消定时器，以防万一
        global_prediction_state_manager.set_music_fadeout_timer(None)

def _play_sound_with_pygame_non_blocking(sound_path, sound_type="未知", duration_ms=None):
    global PYGAME_MIXER_AVAILABLE # music_fadeout_timer is now managed by PredictionStateManager
    
    if not PYGAME_MIXER_AVAILABLE or not pygame.mixer.get_init():
        print(f"!!! [{sound_type} Sound] Pygame mixer 不可用或未初始化。")
        return False
    
    try:
        if pygame.mixer.music.get_busy():
            print(f"  [Pygame Sound] 检测到音乐正忙，先停止当前播放...")
            pygame.mixer.music.stop()
            pygame.mixer.music.unload() 
        
        pygame.mixer.music.load(sound_path)
        print(f"  [Pygame Sound] 已加载: {sound_path}")
        
        pygame.mixer.music.play() # 开始播放
        print(f"  [Pygame Sound] 开始播放 '{sound_type}'。")

        # 如果之前有定时器，取消它
        existing_timer = global_prediction_state_manager.get_music_fadeout_timer()
        if existing_timer and existing_timer.is_alive():
            existing_timer.cancel()
            global_prediction_state_manager.set_music_fadeout_timer(None)
            print(f"  [Pygame Sound] 取消了之前的淡出定时器。")

        if duration_ms and duration_ms > 0:
            # 创建一个新的定时器，在 duration_ms 毫秒后调用 _stop_music_after_duration
            # 注意：实际淡出时间是在 _stop_music_after_duration 中定义的 fadeout_duration
            # duration_ms 是指从播放开始到 *开始淡出* 的时间（或如果不想淡出，则是到停止的时间）
            
            # 如果 duration_ms 很短，可能淡出效果不明显或不需要
            actual_play_duration_seconds = duration_ms / 1000.0
            print(f"  [Pygame Sound] 计划在 {actual_play_duration_seconds:.1f} 秒后开始淡出/停止。")
            
            new_timer = threading.Timer(actual_play_duration_seconds, _stop_music_after_duration)
            new_timer.daemon = True # 确保主程序退出时此线程也退出
            new_timer.start()
            global_prediction_state_manager.set_music_fadeout_timer(new_timer)
        else:
            # 如果没有提供 duration_ms，则完整播放音频
            print(f"  [Pygame Sound] 未指定播放时长，将完整播放。")
            global_prediction_state_manager.set_music_fadeout_timer(None) # 确保没有活动的定时器

        return True
        
    except pygame.error as pg_err: # 更具体地捕获pygame的错误
        print(f"!!! [{sound_type} Sound] Pygame 播放 '{sound_path}' 时发生 Pygame 错误: {pg_err}")
        traceback.print_exc(limit=1)
        return False
    except Exception as e_pg:
        print(f"!!! [{sound_type} Sound] Pygame 播放 '{sound_path}' 时发生未知错误: {e_pg}")
        traceback.print_exc(limit=1)
        return False


def play_signal_alert_sound(signal_type: str):
    current_time = time.time()
    last_alert_time = global_prediction_state_manager.get_last_signal_alert_time()
    if not (getattr(config, 'ALERT_SOUND_ENABLED', False) and (current_time - last_alert_time > 3)): return
    sound_played, custom_sound_path, sound_desc = False, None, "未知"
    if signal_type == "UP": custom_sound_path, sound_desc = getattr(config, 'CUSTOM_UP_SIGNAL_SOUND_PATH', None), "做多"
    elif signal_type == "DOWN": custom_sound_path, sound_desc = getattr(config, 'CUSTOM_DOWN_SIGNAL_SOUND_PATH', None), "做空"
    else: return
    if PYGAME_MIXER_AVAILABLE and custom_sound_path and os.path.exists(custom_sound_path):
        if _play_sound_with_pygame_non_blocking(custom_sound_path, sound_desc + "信号", getattr(config, 'CUSTOM_SIGNAL_SOUND_DURATION_MS', None)): sound_played = True
    if not sound_played:
        if _gui_root and _gui_root.winfo_exists():
            try: gui.update_gui_safe(_gui_root.bell); sound_played = True
            except: pass
        else: print('\a', end='', flush=True); sound_played = True
    if sound_played: global_prediction_state_manager.update_last_signal_alert_time(current_time)

def play_pre_prediction_alarm(target_name_for_alarm):
    current_time = time.time()
    last_alarm_time = global_prediction_state_manager.get_last_pre_alarm_play_time(target_name_for_alarm)
    if not (getattr(config, 'PRE_PREDICTION_ALARM_ENABLED', False) and \
            (current_time - last_alarm_time > getattr(config, 'PRE_ALARM_COOLDOWN_SECONDS', 70))): return
    sound_played, custom_alarm_path = False, getattr(config, 'CUSTOM_PRE_ALARM_SOUND_PATH', None)
    if PYGAME_MIXER_AVAILABLE and custom_alarm_path and os.path.exists(custom_alarm_path):
        if _play_sound_with_pygame_non_blocking(custom_alarm_path, f"预告({target_name_for_alarm})", getattr(config, 'CUSTOM_PRE_ALARM_SOUND_DURATION_MS', None)): sound_played = True
    if sound_played: global_prediction_state_manager.update_last_pre_alarm_play_time(target_name_for_alarm, current_time)

# --- 获取实时价格 (保持不变) ---
def get_real_time_price(binance_client_unused, symbol_override=None):
    # ... (与你之前版本一致) ...
    symbol_to_get = symbol_override if symbol_override else getattr(config, 'SYMBOL', 'BTCUSDT')
    ws_price = realtime_data_manager.get_latest_price(symbol_to_get)
    if ws_price is not None: return float(ws_price)
    url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol_to_get}"
    headers = {'User-Agent': 'Mozilla/5.0'}
    for _ in range(2):
        try:
            response = requests.get(url, timeout=1.5, headers=headers); response.raise_for_status()
            data = response.json();
            if data and 'price' in data: return float(data['price'])
        except: time.sleep(0.3)
    print(f"!!! 严重警告: 多次尝试后仍未能获取 {symbol_to_get} 的价格。")
    return None




# --- 金额计算策略函数 (核心修改处) ---
def calculate_trade_amount_from_strategy(target_config_static, signal_direction, probability_value):
    # strategy_execution_counters is now managed by PredictionStateManager
    global dynamic_config_manager # 使用全局实例

    # 🎯 导入动态胜率跟踪器
    from .dynamic_win_rate_tracker import get_dynamic_win_rate_tracker

    strategy = target_config_static.get('trade_amount_strategy', 'fixed')
    calculated_amount_float = 0.0
    target_name = target_config_static.get('name', 'unknown_target_for_amount_calc')

    # 获取此目标的动态参数 (混合了 dynamic_params.json 和 config.py 的默认值)
    # target_config_static 用于提供静态配置中的默认值给 get_target_params
    dynamic_params_for_target = dynamic_config_manager.get_target_params(target_name, target_config_static)

    if strategy == "fixed":
        # 固定金额通常从静态配置读取，除非你也想让它完全动态化
        calculated_amount_float = float(target_config_static.get('fixed_trade_amount', 5.0))
        # print(f"调试 [{target_name} Amount Fixed]: 使用固定金额: {calculated_amount_float:.2f}")

    elif strategy == "simple_prob_tiered":
        # 分层概率金额的配置也通常来自静态配置
        params_tiered = target_config_static.get('simple_prob_tiered_params')
        if params_tiered and isinstance(params_tiered.get('tiers'), list):
            temp_amount = None
            for tier in sorted(params_tiered['tiers'], key=lambda x: x['min_prob'], reverse=True):
                if probability_value >= tier['min_prob']:
                    temp_amount = float(tier['amount'])
                    break
            if temp_amount is None:
                calculated_amount_float = float(params_tiered.get('default_amount_if_below_tiers', 5.0))
            else:
                calculated_amount_float = temp_amount
            # print(f"调试 [{target_name} Amount Tiered]: 基于概率 {probability_value:.3f}，计算金额: {calculated_amount_float:.2f}")
        else:
            print(f"警告 [{target_name} Amount Tiered]: simple_prob_tiered 策略配置不完整，使用固定金额。")
            calculated_amount_float = float(target_config_static.get('fixed_trade_amount', 5.0))

    elif strategy == "kelly_config":
        # 使用从 dynamic_params_for_target 获取的值
        min_bet_kelly_cfg = float(dynamic_params_for_target.get('min_bet_kelly', 5.0))
        max_bet_kelly_cfg = float(dynamic_params_for_target.get('max_bet_kelly', 200.0))

        enable_initial_conservative = dynamic_params_for_target.get('enable_initial_conservative_betting', False)
        if enable_initial_conservative:
            conservative_trades_threshold = int(dynamic_params_for_target.get('initial_conservative_trades_count', 20))
            current_execution_count_for_target = global_prediction_state_manager.get_strategy_execution_counter(target_name)
            if current_execution_count_for_target < conservative_trades_threshold:
                conservative_amount = float(dynamic_params_for_target.get('initial_conservative_bet_amount', min_bet_kelly_cfg))
                calculated_amount_float = max(min_bet_kelly_cfg, min(conservative_amount, max_bet_kelly_cfg))
                print(f"调试 [{target_name} Amount Kelly - Conservative]: 使用保守金额: {calculated_amount_float:.2f}")
                return calculated_amount_float

        # 🎯 使用动态胜率替代静态胜率
        win_rate_tracker = get_dynamic_win_rate_tracker()

        # 获取静态胜率作为默认值
        static_win_rate = float(dynamic_params_for_target.get('win_rate_p_estimate', 0.51))

        # 获取动态胜率
        dynamic_win_rate, settled_trades_count = win_rate_tracker.get_dynamic_win_rate(
            target_name=target_name,
            min_trades=10,  # 最少10笔交易才使用动态胜率
            default_rate=static_win_rate
        )

        # 🎯 核心优化建议2.3：动态风险敞口模型
        # 根据市场确定性和模型置信度动态调整max_kelly_fraction_f
        dynamic_max_kelly_fraction = _calculate_dynamic_kelly_fraction(
            target_config=dynamic_params_for_target,
            probability_value=probability_value,
            signal_direction=signal_direction,
            target_name=target_name
        )

        # 🎯 实现平滑胜率过渡，避免凯利仓位剧烈波动
        # 权重因子：交易50次后完全信任动态胜率，之前为平滑过渡
        blending_weight = min(1.0, settled_trades_count / 50.0)

        # 平滑混合静态和动态胜率
        p_estimate = (1 - blending_weight) * static_win_rate + blending_weight * dynamic_win_rate

        # 详细的胜率来源说明
        if settled_trades_count < 10:
            win_rate_source = f"静态胜率(交易数量不足: {settled_trades_count}/10)"
        elif settled_trades_count < 50:
            win_rate_source = f"平滑胜率(静态{(1-blending_weight)*100:.0f}%+动态{blending_weight*100:.0f}%, 基于{settled_trades_count}笔交易)"
        else:
            win_rate_source = f"动态胜率(完全信任, 基于{settled_trades_count}笔交易)"

        print(f"调试 [{target_name} Kelly - 胜率平滑]: 静态={static_win_rate:.3f}, 动态={dynamic_win_rate:.3f}, 最终={p_estimate:.3f} (混合权重: {blending_weight:.2f})")

        # 其他凯利参数
        b_payout = float(dynamic_params_for_target.get('payout_ratio_b', 0.8)) # 赔率通常是固定的
        reference_total_capital = float(dynamic_params_for_target.get('virtual_total_capital_for_kelly', 0.0))
        # 🎯 使用动态调整后的max_kelly_fraction_f
        max_f_config = dynamic_max_kelly_fraction

        if reference_total_capital <= 0:
            print(f"警告 [{target_name} Amount Kelly]: 参考总资本无效 ({reference_total_capital})，使用最小凯利下注。")
            calculated_amount_float = min_bet_kelly_cfg
        else:
            q_loss_rate = 1.0 - p_estimate
            if b_payout == 0: kelly_fraction_raw = -1.0
            else: kelly_fraction_raw = p_estimate - (q_loss_rate / b_payout)
            
            print(f"调试 [{target_name} Amount Kelly - Calculation]: p_est={p_estimate:.3f} ({win_rate_source}), b={b_payout:.2f} (静态), ref_capital={reference_total_capital:.2f} (动态), max_f={max_f_config:.3f} (动态), raw_f={kelly_fraction_raw:.4f}")

            if kelly_fraction_raw > 0:
                f_applied_to_capital = min(kelly_fraction_raw, max_f_config)
                base_kelly_amount_float = reference_total_capital * f_applied_to_capital
                calculated_amount_float = max(min_bet_kelly_cfg, min(base_kelly_amount_float, max_bet_kelly_cfg))
                calculated_amount_float = min(calculated_amount_float, reference_total_capital)

                # 🎯 详细的凯利公式优化调试信息
                capital_utilization = calculated_amount_float / reference_total_capital
                is_capped_by_max_f = kelly_fraction_raw > max_f_config
                is_capped_by_min_bet = calculated_amount_float == min_bet_kelly_cfg
                is_capped_by_max_bet = calculated_amount_float == max_bet_kelly_cfg

                print(f"调试 [{target_name} Amount Kelly - 详细]: 基础金额={base_kelly_amount_float:.2f}, 最终金额={calculated_amount_float:.2f}")
                print(f"调试 [{target_name} Amount Kelly - 约束]: 资本利用率={capital_utilization:.1%}, 受max_f限制={'是' if is_capped_by_max_f else '否'}, 受min_bet限制={'是' if is_capped_by_min_bet else '否'}, 受max_bet限制={'是' if is_capped_by_max_bet else '否'}")
            else:
                calculated_amount_float = float(dynamic_params_for_target.get('min_bet_if_kelly_negative', min_bet_kelly_cfg))
                print(f"调试 [{target_name} Amount Kelly]: 凯利分数 ({kelly_fraction_raw:.4f}) <= 0，使用最小金额: {calculated_amount_float:.2f}")
                print(f"调试 [{target_name} Amount Kelly - 负值原因]: 胜率({p_estimate:.3f})过低或赔率({b_payout:.2f})不足")
    else:
        print(f"警告 [{target_name} Amount Strategy]: 未知的 trade_amount_strategy '{strategy}'，使用固定金额。")
        calculated_amount_float = float(target_config_static.get('fixed_trade_amount', 5.0))

    if calculated_amount_float <= 0:
        # print(f"警告 [{target_name} Amount Final Check]: 最终金额 {calculated_amount_float:.2f} <= 0，调整为1.0。")
        calculated_amount_float = 1.0
    return calculated_amount_float


def _calculate_dynamic_kelly_fraction(target_config, probability_value, signal_direction, target_name):
    """
    🎯 核心优化建议2.3：动态风险敞口模型

    根据市场确定性和模型置信度动态调整max_kelly_fraction_f

    Args:
        target_config: 目标配置字典
        probability_value: 模型预测概率
        signal_direction: 信号方向 ("UP" 或 "DOWN")
        target_name: 目标名称

    Returns:
        float: 动态调整后的max_kelly_fraction_f
    """
    try:
        # 获取基础配置
        base_max_kelly = float(target_config.get('max_kelly_fraction_f', 0.1))

        # 🎯 1. 模型置信度评分 (基于概率值)
        # 概率越接近极值(0或1)，置信度越高
        confidence_score = abs(probability_value - 0.5) * 2  # 将[0.5, 1.0]映射到[0, 1]
        confidence_score = min(confidence_score, 1.0)  # 确保不超过1

        # 🎯 2. 市场确定性评分 (基于市场状态特征)
        try:
            # 尝试从全局状态获取市场数据
            from src.core.application_state import ApplicationState
            app_state = ApplicationState.get_instance()

            # 获取全局市场数据
            global_market_data = app_state.get_global_market_data()

            if global_market_data and len(global_market_data) > 0:
                # 使用真实的市场数据计算确定性
                atr_percent = global_market_data.get('global_atr_percent', 1.5)
                adx_value = global_market_data.get('global_adx', 25.0)
                rsi_value = global_market_data.get('global_rsi', 50.0)

                # 计算市场确定性评分
                # 1. 波动率评分：适中波动最好，过高或过低都降低确定性
                if 0.8 <= atr_percent <= 2.0:
                    volatility_score = 1.0  # 理想波动率
                elif 0.5 <= atr_percent <= 3.0:
                    volatility_score = 0.7  # 可接受波动率
                else:
                    volatility_score = 0.3  # 极端波动率

                # 2. 趋势强度评分：ADX越高，趋势越明确
                trend_score = min(1.0, max(0.0, (adx_value - 15) / 30))  # ADX 15-45映射到0-1

                # 3. RSI评分：避免极端超买超卖
                if 30 <= rsi_value <= 70:
                    rsi_score = 1.0  # 正常区间
                elif 20 <= rsi_value <= 80:
                    rsi_score = 0.7  # 轻微极端
                else:
                    rsi_score = 0.3  # 严重极端

                # 综合市场确定性评分
                market_certainty_score = (0.4 * volatility_score + 0.4 * trend_score + 0.2 * rsi_score)

                print(f"  调试 [{target_name} Market Certainty]: ATR={atr_percent:.2f}({volatility_score:.2f}), ADX={adx_value:.1f}({trend_score:.2f}), RSI={rsi_value:.1f}({rsi_score:.2f}) → 确定性={market_certainty_score:.3f}")

            else:
                # 没有全局市场数据，使用默认值
                market_certainty_score = 0.5
                print(f"  警告 [{target_name} Dynamic Kelly]: 无全局市场数据，使用默认确定性评分")

        except Exception as e:
            print(f"  警告 [{target_name} Dynamic Kelly]: 无法获取市场确定性评分: {e}，使用默认值")
            market_certainty_score = 0.5  # 默认中等确定性

        # 🎯 3. 综合确定性评分
        # 权重：模型置信度60% + 市场确定性40%
        composite_certainty = 0.6 * confidence_score + 0.4 * market_certainty_score

        # 🎯 4. 动态调整策略
        # 高确定性时：允许更高的凯利比例 (最高可达基础值的2倍)
        # 低确定性时：大幅降低凯利比例 (最低为基础值的0.2倍)

        if composite_certainty > 0.8:
            # 高确定性：P(signal) > 0.9 且市场状态良好
            kelly_multiplier = 1.5 + 0.5 * composite_certainty  # 1.9-2.0倍
        elif composite_certainty > 0.6:
            # 中高确定性：适度增加
            kelly_multiplier = 1.0 + 0.5 * composite_certainty  # 1.3-1.4倍
        elif composite_certainty > 0.4:
            # 中等确定性：保持基础值
            kelly_multiplier = 0.8 + 0.4 * composite_certainty  # 0.96-1.12倍
        elif composite_certainty > 0.2:
            # 低确定性：显著降低
            kelly_multiplier = 0.4 + 0.4 * composite_certainty  # 0.48-0.56倍
        else:
            # 极低确定性：最保守
            kelly_multiplier = 0.2 + 0.2 * composite_certainty  # 0.2-0.24倍

        # 计算最终的动态凯利比例
        dynamic_kelly_fraction = base_max_kelly * kelly_multiplier

        # 🎯 5. 安全约束
        # 确保动态凯利比例在合理范围内
        min_kelly = base_max_kelly * 0.1   # 最低为基础值的10%
        max_kelly = base_max_kelly * 2.5   # 最高为基础值的250%

        dynamic_kelly_fraction = max(min_kelly, min(dynamic_kelly_fraction, max_kelly))

        # 🎯 6. 详细日志
        print(f"调试 [{target_name} Dynamic Kelly]: 概率={probability_value:.3f}, 方向={signal_direction}")
        print(f"调试 [{target_name} Dynamic Kelly]: 模型置信度={confidence_score:.3f}, 市场确定性={market_certainty_score:.3f}")
        print(f"调试 [{target_name} Dynamic Kelly]: 综合确定性={composite_certainty:.3f}, 倍数={kelly_multiplier:.2f}")
        print(f"调试 [{target_name} Dynamic Kelly]: 基础凯利={base_max_kelly:.3f} → 动态凯利={dynamic_kelly_fraction:.3f}")

        return dynamic_kelly_fraction

    except Exception as e:
        print(f"  警告 [{target_name} Dynamic Kelly]: 动态凯利计算失败: {e}，使用基础值")
        return float(target_config.get('max_kelly_fraction_f', 0.1))


def _load_model_artifacts(target_config_static, target_name):
    """辅助函数：加载模型、Scaler和相关元数据，包括每个fold的最优阈值。"""
    model_dir = target_config_static.get('model_save_dir')
    if not model_dir:
        return None, None, "Error: model_save_dir not found", False, None, None

    target_name_for_file = target_name.replace('/', '_').replace(':', '_')
    minutes_display_val_str = str(target_config_static.get('prediction_minutes_display', '?'))
    if minutes_display_val_str == '?':
        return None, None, f"Error: prediction_minutes_display not set for {target_name}", False, None, None

    meta_filename = f"model_meta_{target_name_for_file}_{minutes_display_val_str}m.json"
    meta_file_path = os.path.join(model_dir, meta_filename)
    if not os.path.exists(meta_file_path):
        return None, None, f"Error: Meta file '{meta_filename}' missing for {target_name}", False, None, None

    try:
        with open(meta_file_path, 'r') as f_meta:
            model_meta = json.load(f_meta)
    except Exception as e:
        return None, None, f"Error loading meta file '{meta_filename}': {e}", False, None, None

    scaler_filename = model_meta.get("scaler_filename")
    if not scaler_filename:
        return None, None, f"Error: Scaler filename not in meta for {target_name}", False, model_meta, None
    scaler_path = os.path.join(model_dir, scaler_filename)
    if not os.path.exists(scaler_path):
        return None, None, f"Error: Scaler file '{scaler_filename}' missing for {target_name}", False, model_meta, None

    try:
        scaler = joblib.load(scaler_path)
        if not scaler:
             raise ValueError("Scaler loaded as None")
    except Exception as e:
        return None, None, f"Error loading scaler '{scaler_filename}': {e}", False, model_meta, None

    loaded_models = []
    fold_thresholds = []  # 新增：存储每个fold的最优阈值
    model_type_str = "Unknown"
    fold_artifacts = model_meta.get("fold_model_artifacts", [])

    if not fold_artifacts: # Legacy: try loading single model
        # print(f"  Info ({target_name}): No 'fold_model_artifacts' in meta, attempting single model load...")
        single_model_base_fn = model_meta.get("model_filename_base")
        single_model_calib_fn = model_meta.get("calibrated_model_filename")
        model_to_load_path = None
        use_calib = target_config_static.get('enable_probability_calibration', True)

        if use_calib and single_model_calib_fn and os.path.exists(os.path.join(model_dir, single_model_calib_fn)):
            model_to_load_path = os.path.join(model_dir, single_model_calib_fn)
            model_type_str = f"Calibrated (Single): {os.path.basename(single_model_calib_fn)}"
        elif single_model_base_fn and os.path.exists(os.path.join(model_dir, single_model_base_fn)):
            model_to_load_path = os.path.join(model_dir, single_model_base_fn)
            model_type_str = f"Original (Single): {os.path.basename(single_model_base_fn)}"

        if model_to_load_path:
            try:
                loaded_models.append(joblib.load(model_to_load_path))
                # 为单模型加载阈值
                threshold = data_utils.load_threshold_from_model_metadata(
                    model_to_load_path, target_name=target_name, default_threshold=0.5
                )
                fold_thresholds.append(threshold)
            except Exception as e:
                return None, scaler, f"Error loading single model '{os.path.basename(model_to_load_path)}': {e}", False, model_meta, None
        else:
            return None, scaler, f"Error: No fold_model_artifacts and no valid single model path for {target_name}", False, model_meta, None
    else: # Ensemble model with folds
        for idx, fold_info in enumerate(fold_artifacts):
            model_fn_key = "model_filename"
            use_calib_fold = target_config_static.get('enable_probability_calibration', True)
            if use_calib_fold and fold_info.get("calibrated_model_filename"):
                model_fn_key = "calibrated_model_filename"

            model_filename = fold_info.get(model_fn_key)
            if not model_filename:
                print(f"  Warning ({target_name}): Fold {idx} missing valid model filename in meta, skipping.")
                continue

            model_path = os.path.join(model_dir, model_filename)
            if os.path.exists(model_path):
                try:
                    loaded_models.append(joblib.load(model_path))
                    # 为每个fold加载其最优阈值
                    threshold = data_utils.load_threshold_from_model_metadata(
                        model_path, target_name=target_name, default_threshold=0.5
                    )
                    fold_thresholds.append(threshold)
                    print(f"  Info ({target_name}): Fold {idx} 阈值: {threshold:.4f}")
                except Exception as e:
                    print(f"  Warning ({target_name}): Failed to load Fold {idx} model '{model_filename}': {e}, skipping.")
            else:
                print(f"  Warning ({target_name}): Fold {idx} model file '{model_filename}' not found, skipping.")

        if loaded_models:
            model_type_str = f"Ensemble ({len(loaded_models)} folds)"
        else:
            return None, scaler, f"Error: Failed to load any models from 'fold_model_artifacts' for {target_name}", False, model_meta, None

    if not loaded_models:
        return None, scaler, f"Error: No models were successfully loaded for {target_name}", False, model_meta, None

    # 确保fold_thresholds和loaded_models数量一致
    if len(fold_thresholds) != len(loaded_models):
        print(f"  Warning ({target_name}): 阈值数量({len(fold_thresholds)})与模型数量({len(loaded_models)})不匹配，用默认阈值填充")
        while len(fold_thresholds) < len(loaded_models):
            fold_thresholds.append(0.5)

    return loaded_models, scaler, model_type_str, True, model_meta, fold_thresholds


# --- 核心预测周期函数 ---
def run_prediction_cycle_for_target(target_config_static, binance_client, app_timezone_param,
                                    return_core_prediction_only=False,
                                    simulator_actual_url=None):
    # --- 初始变量定义 ---
    target_name = target_config_static.get('name', 'unknown_target')
    interval_main = target_config_static.get('interval', 'unknown_interval')
    symbol_to_use = target_config_static.get('symbol', getattr(config, 'SYMBOL', 'BTCUSDT'))
    
    prediction_label = "等待中..."
    prediction_color = config.NEUTRAL_COLOR
    up_prob_display = "N/A"
    down_prob_display = "N/A"
    details = ""
    status_msg_for_gui = f"正在为 '{target_name}' 执行预测..." # 初始状态消息
    status_level_final_gui = "info" # 初始状态级别
    
    avg_up_prob_value = np.nan
    avg_down_prob_value = np.nan
    model_positive_class_prob = np.nan
    final_signal_for_internal_state = "Error_Initialization"
    any_successful_model_prediction = False

    actual_signal_for_sound_and_simulator = None
    last_kline_close_main = None # 会在数据获取后赋值
    model_type_loaded_str = "未知模型类型" # 会在模型加载后赋值
    filter_reason_details_for_gui = ""
    # 初始化阈值，将在模型加载后更新为最优阈值
    sig_thresh_use = float(target_config_static.get('signal_threshold', 0.6))
    # 初始化趋势和波动率相关变量，以防过滤器未执行或出错
    current_trend_status_text = "趋势: 未检测/未启用"
    current_volatility_status_text = "波动率: 未检测/未启用"
    higher_tf_trend_signal = 0
    higher_tf_trend_strength = 0
    current_volatility_level = 0
    model_meta = None # 初始化 model_meta

    if not return_core_prediction_only:
        current_gui_root_in_prediction = gui._gui_root if hasattr(gui, '_gui_root') else None
        if current_gui_root_in_prediction and hasattr(current_gui_root_in_prediction, 'winfo_exists') and current_gui_root_in_prediction.winfo_exists():
            if hasattr(gui, 'update_status'):
                gui.update_gui_safe(gui.update_status, status_msg_for_gui, status_level_final_gui)

    try: # <--- 这是最外层的 try 块，包裹所有核心逻辑
        # === 步骤 1: 加载模型、Scaler和阈值 (使用辅助函数) ===
        loaded_ensemble_models, scaler, model_type_loaded_str, load_success, model_meta_from_helper, fold_thresholds = \
            _load_model_artifacts(target_config_static, target_name)

        if not load_success:
            # model_type_loaded_str from _load_model_artifacts already contains the error message
            raise ValueError(f"'{target_name}': Model/Scaler loading failed. Reason: {model_type_loaded_str}")

        # 如果加载成功，model_meta 应该由辅助函数返回，这里我们将其赋值给函数作用域内的 model_meta
        # 注意：原来的 model_meta 初始化是在 try 块之外，现在它依赖于 _load_model_artifacts 的成功返回
        model_meta = model_meta_from_helper
        if model_meta is None: # 确保 model_meta 在加载成功后不是 None
            raise ValueError(f"'{target_name}': model_meta is None even after successful artifact loading.")

        # 检查是否启用了独立阈值投票机制
        use_individual_thresholds = target_config_static.get('use_individual_fold_thresholds', getattr(config, 'USE_INDIVIDUAL_FOLD_THRESHOLDS', False))
        if use_individual_thresholds and fold_thresholds and len(fold_thresholds) == len(loaded_ensemble_models):
            print(f"  ({target_name}): 启用独立阈值投票机制，每个fold使用自己的最优阈值")
        else:
            print(f"  ({target_name}): 使用传统平均概率机制")

        # === 加载最优决策阈值 ===
        optimal_threshold_loaded = None
        try:
            # 🎯 优先尝试从主模型元数据文件中加载集成模型最优阈值
            model_dir = target_config_static.get('model_save_dir')
            if model_dir:
                # 构建主模型元数据文件路径
                pred_periods = target_config_static.get('prediction_periods', [1])
                pred_period = pred_periods[0] if isinstance(pred_periods, list) and pred_periods else 1
                interval_str = target_config_static.get('interval', 'Xm').rstrip('mhd')
                try:
                    minutes_display = int(interval_str) * pred_period
                except ValueError:
                    minutes_display = target_config_static.get('prediction_minutes_display', '?')

                target_name_for_file = target_name.replace('/', '_').replace(':', '_')
                main_meta_filename = f"model_meta_{target_name_for_file}_{minutes_display}m.json"
                main_meta_path = os.path.join(model_dir, main_meta_filename)

                # 尝试加载集成模型最优阈值
                ensemble_threshold = data_utils.load_ensemble_threshold_from_main_metadata(
                    main_meta_path,
                    target_name=target_name,
                    default_threshold=None  # 使用None表示未找到
                )

                if ensemble_threshold is not None:
                    optimal_threshold_loaded = ensemble_threshold
                    print(f"  ({target_name}): 成功加载集成模型最优阈值 {optimal_threshold_loaded:.4f}")
                else:
                    print(f"  ({target_name}): 未找到集成模型阈值，尝试加载单模型阈值")

                    # 回退到从第一个模型的元数据中加载最优阈值
                    if model_meta and 'model_files' in model_meta and model_meta['model_files']:
                        first_model_path = model_meta['model_files'][0]
                        if os.path.exists(first_model_path):
                            optimal_threshold_loaded = data_utils.load_threshold_from_model_metadata(
                                first_model_path,
                                target_name=target_name,
                                default_threshold=target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                            )
                            print(f"  ({target_name}): 成功加载单模型最优阈值 {optimal_threshold_loaded:.4f}")
                        else:
                            print(f"  ({target_name}): 模型文件不存在，使用默认阈值")
                            optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                    else:
                        print(f"  ({target_name}): 无模型元数据，使用默认阈值")
                        optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
            else:
                print(f"  ({target_name}): 无模型目录，使用默认阈值")
                optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))

        except Exception as e_threshold_load:
            print(f"  !!! ({target_name}): 加载最优阈值失败: {e_threshold_load}，使用默认阈值")
            optimal_threshold_loaded = target_config_static.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))

        # 更新决策阈值为加载的最优阈值或集成模型预测阈值
        ensemble_threshold_override = target_config_static.get('ensemble_prediction_threshold_override', False)
        ensemble_prediction_threshold = target_config_static.get('ensemble_prediction_threshold')

        if ensemble_threshold_override and ensemble_prediction_threshold is not None:
            # 使用集成模型预测阈值覆盖最优阈值
            sig_thresh_use = float(ensemble_prediction_threshold)
            print(f"  ({target_name}): 决策阈值已设置为集成模型预测阈值 {sig_thresh_use:.4f} (覆盖最优阈值)")
        elif optimal_threshold_loaded is not None:
            sig_thresh_use = float(optimal_threshold_loaded)
            print(f"  ({target_name}): 决策阈值已更新为最优阈值 {sig_thresh_use:.4f}")
        else:
            print(f"  ({target_name}): 使用配置中的默认阈值 {sig_thresh_use:.4f}")

        # === 步骤 2: 获取K线数据 ===
        df_klines_main_raw = fetch_binance_history(binance_client, symbol_to_use, target_config_static.get('interval'), limit=target_config_static.get('prediction_fetch_limit', 350))
        if df_klines_main_raw is None or df_klines_main_raw.empty: raise ValueError(f"未能获取 {symbol_to_use}@{target_config_static.get('interval')} K线 for '{target_name}'")
        # +++ 数据验证 +++
        # 现在使用 df_klines_main_raw 进行验证
        validation_result_pred = DataValidator.validate_ohlcv_data(df_klines_main_raw.copy())
        
        if not validation_result_pred['is_valid']:
            error_msg_pred_val = f"预测用K线数据 for '{target_name}' 未通过验证: {validation_result_pred['errors']}"
            print(f"!!! {error_msg_pred_val}")
            if return_core_prediction_only:
                return {"p_favorable": np.nan, "p_up": np.nan, "p_down": np.nan,
                        "internal_signal": f"Error_DataValidation_{target_name}", "error": True}
            raise ValueError(error_msg_pred_val)
            
        if validation_result_pred['warnings']:
            print(f"  警告 ({target_name} 预测): 数据验证发现问题（已尝试清理）: {validation_result_pred['warnings']}")
        
        # 使用验证和可能清理过的数据赋值给 df_klines_main
        df_klines_main = validation_result_pred['cleaned_data']
        # +++ 验证结束 +++


        min_hist_bars_pred = target_config_static.get('min_historical_bars_for_prediction', 100)
        if len(df_klines_main) < min_hist_bars_pred: raise ValueError(f"K线数据过少 ({len(df_klines_main)}条) for '{target_name}'")
        last_kline_close_main = df_klines_main['close'].iloc[-1]





        # === 步骤 3: 特征准备和模型预测 ===
        # 传递 model_meta 给 prepare_features_for_prediction 函数，以便从元数据中读取特征列表
        scaled_X_for_model = prepare_features_for_prediction(df_klines_main.copy(), client=binance_client, scaler=scaler, target_config=target_config_static, model_meta=model_meta)
        if scaled_X_for_model is None or scaled_X_for_model.shape[0] != 1:
            raise ValueError(f"准备预测特征失败 for '{target_name}'")

        # === 获取每个模型的概率和决策 ===
        all_probas_from_ensemble = []
        individual_decisions = []  # 存储每个fold的独立决策

        for i, ensemble_member_model in enumerate(loaded_ensemble_models):
            try:
                proba_single_member = ensemble_member_model.predict_proba(scaled_X_for_model)[0]
                if not (isinstance(proba_single_member, np.ndarray) and len(proba_single_member) == 2 and not np.isnan(proba_single_member).any()):
                    print(f"  !!! 警告 ({target_name}): 集成成员 {i} 返回了无效的概率值: {proba_single_member}，将使用中性概率 [0.5, 0.5]。")
                    proba_single_member = np.array([0.5, 0.5])

                all_probas_from_ensemble.append(proba_single_member)

                # 如果启用独立阈值投票，使用该fold的最优阈值进行决策
                if use_individual_thresholds and fold_thresholds and i < len(fold_thresholds):
                    fold_threshold = fold_thresholds[i]
                    # 根据模型类型判断使用哪个概率
                    target_variable_type = target_config_static.get('target_variable_type', 'BOTH').upper()
                    if target_variable_type == "UP_ONLY":
                        fold_decision = "UP" if proba_single_member[1] > fold_threshold else "Neutral"
                    elif target_variable_type == "DOWN_ONLY":
                        fold_decision = "DOWN" if proba_single_member[1] > fold_threshold else "Neutral"
                    elif target_variable_type == "BOTH":
                        if proba_single_member[1] > fold_threshold:
                            fold_decision = "UP"
                        elif proba_single_member[0] > fold_threshold:
                            fold_decision = "DOWN"
                        else:
                            fold_decision = "Neutral"
                    else:
                        fold_decision = "Neutral"

                    individual_decisions.append(fold_decision)
                    print(f"    Fold {i}: 概率={proba_single_member}, 阈值={fold_threshold:.4f}, 决策={fold_decision}")

            except Exception as e_pred_member:
                print(f"  !!! 警告 ({target_name}): 集成成员 {i} 预测时出错: {e_pred_member}，将使用中性概率 [0.5, 0.5]。")
                all_probas_from_ensemble.append(np.array([0.5, 0.5]))
                if use_individual_thresholds:
                    individual_decisions.append("Neutral")

        if not all_probas_from_ensemble:
            raise ValueError(f"所有集成模型预测均失败 (无有效概率输出) for target {target_name}")

        # 计算平均概率（用于显示和向后兼容）
        proba_from_model_raw = np.mean(all_probas_from_ensemble, axis=0)
        if not (isinstance(proba_from_model_raw, np.ndarray) and len(proba_from_model_raw) == 2 and not np.isnan(proba_from_model_raw).any()):
            raise ValueError(f"平均集成概率无效 for '{target_name}': {proba_from_model_raw}")

        # === 决策逻辑：独立阈值投票 vs 传统平均概率 ===
        target_variable_type = target_config_static.get('target_variable_type', 'BOTH').upper()

        if use_individual_thresholds and individual_decisions and len(individual_decisions) == len(loaded_ensemble_models):
            # 使用独立阈值投票机制
            up_votes = individual_decisions.count("UP")
            down_votes = individual_decisions.count("DOWN")
            neutral_votes = individual_decisions.count("Neutral")
            total_votes = len(individual_decisions)

            # 投票阈值：需要超过一半的模型同意
            vote_threshold = total_votes // 2 + 1

            if up_votes >= vote_threshold:
                final_signal_for_internal_state = "UP"
                raw_lgbm_signal_internal = "UP"
                model_positive_class_prob = proba_from_model_raw[1]
            elif down_votes >= vote_threshold:
                final_signal_for_internal_state = "DOWN"
                raw_lgbm_signal_internal = "DOWN"
                model_positive_class_prob = proba_from_model_raw[0] if target_variable_type == "DOWN_ONLY" else proba_from_model_raw[1]
            else:
                final_signal_for_internal_state = "Neutral"
                raw_lgbm_signal_internal = "Neutral"
                model_positive_class_prob = max(proba_from_model_raw)

            print(f"  ({target_name}): 投票结果 - UP:{up_votes}, DOWN:{down_votes}, Neutral:{neutral_votes}, 最终决策:{final_signal_for_internal_state}")

        else:
            # 使用传统的平均概率机制
            if target_variable_type == "UP_ONLY":
                model_positive_class_prob = proba_from_model_raw[1]
                if model_positive_class_prob > sig_thresh_use:
                    final_signal_for_internal_state = "UP"
                    raw_lgbm_signal_internal = "UP"
                else:
                    final_signal_for_internal_state = "Neutral"
                    raw_lgbm_signal_internal = "Neutral"
            elif target_variable_type == "DOWN_ONLY":
                model_positive_class_prob = proba_from_model_raw[1]
                if model_positive_class_prob > sig_thresh_use:
                    final_signal_for_internal_state = "DOWN"
                    raw_lgbm_signal_internal = "DOWN"
                else:
                    final_signal_for_internal_state = "Neutral"
                    raw_lgbm_signal_internal = "Neutral"
            elif target_variable_type == "BOTH":
                if proba_from_model_raw[1] > sig_thresh_use:
                    final_signal_for_internal_state = "UP"
                    raw_lgbm_signal_internal = "UP"
                    model_positive_class_prob = proba_from_model_raw[1]
                elif proba_from_model_raw[0] > sig_thresh_use:
                    final_signal_for_internal_state = "DOWN"
                    raw_lgbm_signal_internal = "DOWN"
                    model_positive_class_prob = proba_from_model_raw[0]
                else:
                    final_signal_for_internal_state = "Neutral"
                    raw_lgbm_signal_internal = "Neutral"
                    model_positive_class_prob = max(proba_from_model_raw)
            else:
                raise ValueError(f"未知的 target_variable_type: {target_variable_type} for target {target_name}")

        # 设置用于显示的概率值
        avg_down_prob_value = proba_from_model_raw[0]
        avg_up_prob_value = proba_from_model_raw[1]
        



        any_successful_model_prediction = True # 标记核心预测成功

        # === 初始化趋势和波动率相关变量 (在return_core_prediction_only检查之前) ===
        # 这些变量在核心模式返回时也需要使用
        higher_tf_trend_signal = 0  # -1: 下降趋势, 0: 无明确趋势, 1: 上升趋势
        higher_tf_trend_strength = 0  # 0: 弱趋势, 1: 强趋势
        latest_adx = 0.0
        latest_pdi = 0.0
        latest_mdi = 0.0
        latest_ema_short = 0.0
        latest_ema_long = 0.0
        current_trend_status_text = "趋势: 未计算"
        current_volatility_level = 0  # 0: 正常, 1: 高波动, -1: 低波动
        atr_now = 0.0
        atr_percent = 0.0
        current_volatility_status_text = "波动率: 未计算"

        # === 始终计算趋势和波动率指标 (用于元模型特征工程) ===
        # 即使在核心模式下也要计算，因为元模型需要这些特征
        enable_trend_detection_cfg = target_config_static.get('enable_trend_detection', False)
        enable_volatility_filter_cfg = target_config_static.get('enable_volatility_filter', False)

        # 趋势指标计算
        trend_tf_str_cfg = target_config_static.get('trend_detection_timeframe', interval_main)
        df_for_trend = df_klines_main if trend_tf_str_cfg == interval_main else \
                       fetch_binance_history(binance_client, symbol_to_use, trend_tf_str_cfg, limit=200)
        if df_for_trend is not None and not df_for_trend.empty and len(df_for_trend) >= 50:
            trend_indicator_type_cfg = target_config_static.get('trend_indicator_type', 'adx')
            trend_desc_brief_gui = f"{trend_indicator_type_cfg.upper()}@{trend_tf_str_cfg}"

            if trend_indicator_type_cfg == 'adx':
                adx_p_cfg = target_config_static.get('trend_adx_period',14)
                adx_strength_th_cfg = target_config_static.get('trend_adx_strength_threshold',23)
                adx_general_th_cfg = target_config_static.get('trend_adx_threshold',20)
                adx_df_calc = pta.adx(df_for_trend['high'], df_for_trend['low'], df_for_trend['close'], length=adx_p_cfg, lensig=adx_p_cfg)

                if adx_df_calc is not None and not adx_df_calc.empty:
                    adx_col, pdi_col, mdi_col = f'ADX_{adx_p_cfg}', f'DMP_{adx_p_cfg}', f'DMN_{adx_p_cfg}'
                    if all(c in adx_df_calc.columns for c in [adx_col, pdi_col, mdi_col]):
                        try:
                            adx_val = adx_df_calc[adx_col].iloc[-1]
                            pdi_val = adx_df_calc[pdi_col].iloc[-1]
                            mdi_val = adx_df_calc[mdi_col].iloc[-1]

                            if not any(pd.isna(v) for v in [adx_val, pdi_val, mdi_val]):
                                latest_adx, latest_pdi, latest_mdi = adx_val, pdi_val, mdi_val

                                if latest_adx > adx_general_th_cfg:
                                    higher_tf_trend_signal = 1 if latest_pdi > latest_mdi else (-1 if latest_mdi > latest_pdi else 0)
                                if latest_adx > adx_strength_th_cfg and higher_tf_trend_signal != 0:
                                    higher_tf_trend_strength = 1
                                current_trend_status_text = f"趋势: ADX({adx_p_cfg})={latest_adx:.1f}"
                        except (IndexError, KeyError):
                            pass  # 保持默认值

            elif trend_indicator_type_cfg == 'ema_cross':
                ema_short_p_cfg = target_config_static.get('trend_ema_short_period',20)
                ema_long_p_cfg = target_config_static.get('trend_ema_long_period',50)
                ema_short_calc = pta.ema(df_for_trend['close'], length=ema_short_p_cfg)
                ema_long_calc = pta.ema(df_for_trend['close'], length=ema_long_p_cfg)

                if ema_short_calc is not None and ema_long_calc is not None and not ema_short_calc.empty and not ema_long_calc.empty:
                    try:
                        latest_ema_short, latest_ema_long = ema_short_calc.iloc[-1], ema_long_calc.iloc[-1]
                        if latest_ema_short > latest_ema_long:
                            higher_tf_trend_signal = 1
                        elif latest_ema_short < latest_ema_long:
                            higher_tf_trend_signal = -1
                        current_trend_status_text = f"趋势: EMA({ema_short_p_cfg},{ema_long_p_cfg})"
                    except (IndexError, KeyError):
                        pass  # 保持默认值

        # 波动率指标计算
        vol_tf_cfg = target_config_static.get('volatility_filter_timeframe', interval_main)
        df_for_vol = df_klines_main if vol_tf_cfg == interval_main else \
                     fetch_binance_history(binance_client, symbol_to_use, vol_tf_cfg, limit=target_config_static.get('volatility_atr_period', 14) + 50)
        if df_for_vol is not None and not df_for_vol.empty and len(df_for_vol) >= target_config_static.get('volatility_atr_period', 14) + 1:
            atr_p_vol = target_config_static.get('volatility_atr_period', 14)
            atr_series = pta.atr(df_for_vol['high'], df_for_vol['low'], df_for_vol['close'], length=atr_p_vol)

            if atr_series is not None and not atr_series.empty and not pd.isna(atr_series.iloc[-1]):
                try:
                    atr_now = atr_series.iloc[-1]
                    close_for_atr = df_for_vol['close'].iloc[-1]
                    atr_percent = (atr_now / close_for_atr) * 100 if close_for_atr > 0 else 0.0
                    min_atr_pct = target_config_static.get('volatility_min_atr_percent',0.05)
                    max_atr_pct = target_config_static.get('volatility_max_atr_percent',1.5)

                    if atr_percent < min_atr_pct:
                        current_volatility_level = 1
                    elif atr_percent > max_atr_pct:
                        current_volatility_level = -1
                    else:
                        current_volatility_level = 0
                    current_volatility_status_text = f"波动率: ATR({atr_p_vol})={atr_now:.2f} ({atr_percent:.2f}%)"
                except (IndexError, KeyError):
                    pass  # 保持默认值

        # --- 核心模式的返回逻辑 ---
        if return_core_prediction_only: # 假设这一行是正确的缩进级别 (例如，在 try 块内)
            if not any_successful_model_prediction: # 相对于上面的 if 增加一级缩进
                core_info = { # 相对于 if not ... 增加一级缩进
                    "p_favorable": np.nan,
                    "p_up": np.nan,
                    "p_down": np.nan,
                    "internal_signal": final_signal_for_internal_state,
                    "error": True,
                    # 新增：上下文特征（错误情况下使用默认值）
                    "context_features": {
                        "atr_percent": np.nan,
                        "adx_value": np.nan,
                        "prediction_confidence": np.nan,
                        "rsi_value": np.nan,
                        "volume_ratio": np.nan,
                        "price_change_1p": np.nan
                    }
                }
            else: # 这个 else 与 if not ... 对齐
                # 计算上下文特征
                context_features = {}

                # 1. 波动率特征：ATR百分比
                if not pd.isna(atr_percent):
                    context_features["atr_percent"] = float(atr_percent)
                else:
                    context_features["atr_percent"] = 0.0

                # 2. 趋势强度特征：ADX值
                if not pd.isna(latest_adx):
                    context_features["adx_value"] = float(latest_adx)
                else:
                    context_features["adx_value"] = 0.0

                # 3. 预测置信度：abs(p_favorable - 0.5)
                if not pd.isna(model_positive_class_prob):
                    context_features["prediction_confidence"] = abs(float(model_positive_class_prob) - 0.5)
                else:
                    context_features["prediction_confidence"] = 0.0

                # 4. 从最新特征中提取RSI值
                try:
                    if scaled_X_for_model is not None and hasattr(scaler, 'feature_names_in_'):
                        feature_names = list(scaler.feature_names_in_)
                        feature_values = scaled_X_for_model[0]

                        # 查找RSI特征
                        rsi_features = [i for i, name in enumerate(feature_names) if 'RSI' in name.upper()]
                        if rsi_features:
                            # 使用第一个RSI特征，需要反向缩放获得原始值
                            rsi_scaled = feature_values[rsi_features[0]]
                            # 简化处理：假设RSI在0-100范围内，缩放后的值需要反向处理
                            # 这里使用一个近似值，实际应该用scaler.inverse_transform
                            context_features["rsi_value"] = float(rsi_scaled * 50 + 50)  # 简化的反缩放
                        else:
                            context_features["rsi_value"] = 50.0  # RSI中性值
                    else:
                        context_features["rsi_value"] = 50.0
                except Exception as e_rsi:
                    context_features["rsi_value"] = 50.0

                # 5. 成交量比率（从最新K线数据中获取）
                try:
                    if df_klines_main is not None and not df_klines_main.empty and 'volume' in df_klines_main.columns:
                        recent_volumes = df_klines_main['volume'].tail(20)
                        current_volume = df_klines_main['volume'].iloc[-1]
                        avg_volume = recent_volumes.mean()
                        if avg_volume > 0:
                            context_features["volume_ratio"] = float(current_volume / avg_volume)
                        else:
                            context_features["volume_ratio"] = 1.0
                    else:
                        context_features["volume_ratio"] = 1.0
                except Exception as e_vol:
                    context_features["volume_ratio"] = 1.0

                # 6. 价格变化（1期）
                try:
                    if df_klines_main is not None and not df_klines_main.empty and 'close' in df_klines_main.columns:
                        if len(df_klines_main) >= 2:
                            current_close = df_klines_main['close'].iloc[-1]
                            prev_close = df_klines_main['close'].iloc[-2]
                            context_features["price_change_1p"] = float((current_close - prev_close) / prev_close)
                        else:
                            context_features["price_change_1p"] = 0.0
                    else:
                        context_features["price_change_1p"] = 0.0
                except Exception as e_price:
                    context_features["price_change_1p"] = 0.0

                core_info = { # 相对于 else 增加一级缩进
                    "p_favorable": model_positive_class_prob,
                    "p_up": avg_up_prob_value,
                    "p_down": avg_down_prob_value,
                    "internal_signal": final_signal_for_internal_state,
                    "error": False,
                    # 新增：丰富的上下文特征
                    "context_features": context_features,
                    # 保留原有的趋势和波动率信息（向后兼容）
                    "trend_signal": higher_tf_trend_signal,
                    "trend_strength": higher_tf_trend_strength,
                    "adx_value": latest_adx,
                    "pdi_value": latest_pdi,
                    "mdi_value": latest_mdi,
                    "ema_short": latest_ema_short,
                    "ema_long": latest_ema_long,
                    "volatility_level": current_volatility_level,
                    "atr_value": atr_now,
                    "atr_percent": atr_percent
                }

            return core_info

        # --- 完整模式的后续逻辑 (仍然在 try 块内) ---
        up_prob_display = f"{avg_up_prob_value:.2%}" if not pd.isna(avg_up_prob_value) else "N/A"
        down_prob_display = f"{avg_down_prob_value:.2%}" if not pd.isna(avg_down_prob_value) else "N/A"

        # === 步骤 4, 5, 6: 趋势、波动率、信号决策、过滤 (与您之前版本一致) ===
        # 动态配置获取 (应在过滤器和金额计算前，确保使用最新参数)
        dynamic_params_current_target = dynamic_config_manager.get_target_params(target_name, target_config_static)
        is_target_signal_enabled_dynamically = dynamic_params_current_target.get("enabled", True)
        # is_master_signal_sending_enabled 将在元模型层面或更高层级检查，这里暂时不使用

        # (您的趋势过滤逻辑，它会修改 final_signal_for_internal_state, current_trend_status_text, filter_reason_details_for_gui)
        # (您的波动率过滤逻辑，它会修改 final_signal_for_internal_state, current_volatility_status_text, filter_reason_details_for_gui)
        # (您的动态阈值逻辑，它会修改 sig_thresh_use，并可能基于此再次更新 final_signal_for_internal_state)
        # --- 这部分代码需要您从之前的版本中完整复制过来 ---
        # 为了简洁，这里用占位符表示，请确保您实际代码中有这些过滤步骤
        # --- START OF FILTERING LOGIC PLACEHOLDER ---
        final_signal_for_internal_state = raw_lgbm_signal_internal # 从模型初步信号开始
        filter_reason_details_for_gui = ""

        # === 详细的趋势和波动率分析 (仅在非核心模式下) ===
        # 在核心模式下，基本的趋势和波动率指标已经在前面计算过了
        if not return_core_prediction_only:
            # 添加详细的状态描述和日志
            trend_desc_brief_gui = f"ADX@{trend_tf_str_cfg}" if enable_trend_detection_cfg else "趋势检测已禁用"

            # 更新趋势状态文本
            if latest_adx > 0:
                strength_label = "强劲" if higher_tf_trend_strength == 1 else "一般"
                if higher_tf_trend_signal == 1:
                    current_trend_status_text = f"趋势: {trend_desc_brief_gui} {strength_label}上升"
                elif higher_tf_trend_signal == -1:
                    current_trend_status_text = f"趋势: {trend_desc_brief_gui} {strength_label}下降"
                else:
                    current_trend_status_text = f"趋势: {trend_desc_brief_gui} 盘整/不明"

                # 添加详细信息到details
                details += f"\nADX@{trend_tf_str_cfg}: {latest_adx:.1f} (PDI:{latest_pdi:.1f}, MDI:{latest_mdi:.1f})"
            elif latest_ema_short > 0 and latest_ema_long > 0:
                details += f"\nEMA@{trend_tf_str_cfg}: {latest_ema_short:.2f} vs {latest_ema_long:.2f}"

            # 添加波动率详细信息
            if atr_now > 0:
                details += f"\nATR@{vol_tf_cfg}: {atr_now:.2f} ({atr_percent:.2f}%)"


        # 动态阈值调整 (如果启用)
        enable_dyn_thresh_cfg = target_config_static.get('enable_dynamic_threshold', False)
        if enable_dyn_thresh_cfg:
            base_thresh_dyn = target_config_static.get('dynamic_threshold_base', sig_thresh_use)
            trend_adj_dyn = target_config_static.get('dynamic_threshold_trend_adjust',0.03)
            vol_adj_dyn = target_config_static.get('dynamic_threshold_volatility_adjust',0.02)
            adj_thresh_calc = base_thresh_dyn; adj_reasons_gui = []
            # 动态调整阈值是针对“预测为正类”的概率，所以对于DOWN_ONLY模型，如果它预测的是“下跌”，
            # 那么趋势和波动率对这个“下跌”的确认/否认也应该相应调整。
            # 这里的逻辑假设 sig_thresh_use 总是用于“主要方向”的概率。
            # 如果是UP信号(来自UP_ONLY或BOTH)，且趋势是强劲上升，则降低阈值。
            # 如果是DOWN信号(来自DOWN_ONLY或BOTH)，且趋势是强劲下降，则降低阈值。
            # 反之，如果信号与强趋势相反，则增加阈值。
            # 这是一个简化的例子，你可能需要更细致地处理：

            current_signal_direction_for_dyn_thresh = "NEUTRAL"
            if "UP" in final_signal_for_internal_state: current_signal_direction_for_dyn_thresh = "UP"
            elif "DOWN" in final_signal_for_internal_state: current_signal_direction_for_dyn_thresh = "DOWN"

            if higher_tf_trend_strength == 1: # 强趋势存在
                if higher_tf_trend_signal == 1 and current_signal_direction_for_dyn_thresh == "UP": # 强升，且当前信号是UP
                    adj_thresh_calc -= trend_adj_dyn; adj_reasons_gui.append(f"强升确认-{trend_adj_dyn:.3f}")
                elif higher_tf_trend_signal == -1 and current_signal_direction_for_dyn_thresh == "DOWN": # 强降，且当前信号是DOWN
                    adj_thresh_calc -= trend_adj_dyn; adj_reasons_gui.append(f"强降确认-{trend_adj_dyn:.3f}")
                elif higher_tf_trend_signal == 1 and current_signal_direction_for_dyn_thresh == "DOWN": # 强升，但当前信号是DOWN (逆势)
                    adj_thresh_calc += trend_adj_dyn; adj_reasons_gui.append(f"强升过滤-{trend_adj_dyn:.3f}")
                elif higher_tf_trend_signal == -1 and current_signal_direction_for_dyn_thresh == "UP": # 强降，但当前信号是UP (逆势)
                    adj_thresh_calc += trend_adj_dyn; adj_reasons_gui.append(f"强降过滤-{trend_adj_dyn:.3f}")

            if current_volatility_level != 0 : # 波动率异常（过高或过低）
                adj_thresh_calc += vol_adj_dyn # 增加阈值，更谨慎
                adj_reasons_gui.append(f"{('低' if current_volatility_level==1 else '高')}波+{vol_adj_dyn:.3f}")

            sig_thresh_use = np.clip(adj_thresh_calc, 0.51, target_config_static.get('dynamic_threshold_max_clip', 0.95))
            if not return_core_prediction_only: details += f"\n动态阈P: {sig_thresh_use:.3f} (基:{base_thresh_dyn:.3f},调:{','.join(adj_reasons_gui) if adj_reasons_gui else '无'})"
        elif not return_core_prediction_only: details += f"\n固定阈P: {sig_thresh_use:.3f}" # 使用静态配置的阈值

        # 根据调整后的 sig_thresh_use 重新判断初步信号 (如果启用了动态阈值)
        # 注意：这一步很重要，因为 sig_thresh_use 可能已经变了
        if enable_dyn_thresh_cfg:
            if target_variable_type == "UP_ONLY" and avg_up_prob_value > sig_thresh_use: final_signal_for_internal_state = "UP"
            elif target_variable_type == "DOWN_ONLY" and avg_down_prob_value > sig_thresh_use: final_signal_for_internal_state = "DOWN"
            elif target_variable_type == "BOTH":
                if avg_up_prob_value > sig_thresh_use: final_signal_for_internal_state = "UP"
                elif avg_down_prob_value > sig_thresh_use: final_signal_for_internal_state = "DOWN"
                else: final_signal_for_internal_state = "Neutral" # 如果动态调整后都不满足了
            else: # 如果之前是UP/DOWN但动态调整后不满足了
                final_signal_for_internal_state = "Neutral"


        # 应用波动率过滤器 (如果启用且初步信号不是Neutral)
        if enable_volatility_filter_cfg and current_volatility_level != 0 and final_signal_for_internal_state not in ["Neutral", "Error_Initialization"]:
            filter_reason_details_for_gui += f"\n过滤({('低' if current_volatility_level==1 else '高')}波)!"
            final_signal_for_internal_state = "Neutral_Filtered_Volatility"

        # 应用趋势过滤器 (如果启用，且未被波动率过滤，且有初步信号)
        current_signal_after_vol = final_signal_for_internal_state # 保存波动率过滤后的状态
        if enable_trend_detection_cfg and target_config_static.get('trend_filter_strategy','none') != 'none' and \
           not ("Filtered_Volatility" in current_signal_after_vol or "Error" in current_signal_after_vol or "Neutral" == current_signal_after_vol) :
            trend_action_desc_gui = ""; is_strong_trend = (higher_tf_trend_strength == 1)
            trend_strategy = target_config_static.get('trend_filter_strategy','filter_only')
            # sig_dir_for_trend 现在基于 current_signal_after_vol
            sig_dir_for_trend = "UP" if "UP" in current_signal_after_vol else ("DOWN" if "DOWN" in current_signal_after_vol else "Neutral")

            if higher_tf_trend_signal != 0 and sig_dir_for_trend != "Neutral": # 有趋势且有信号
                if is_strong_trend and trend_strategy in ['filter_only', 'chase_trend']: # 强趋势下的过滤或追逐逻辑
                    if (sig_dir_for_trend == "UP" and higher_tf_trend_signal == -1) or \
                       (sig_dir_for_trend == "DOWN" and higher_tf_trend_signal == 1): # 信号与强趋势相反
                        final_signal_for_internal_state = "Neutral_Filtered_Trend"
                        trend_action_desc_gui = f"趋势:强{('降' if higher_tf_trend_signal == -1 else '升')}过滤{sig_dir_for_trend}"
                    # else: 信号与强趋势同向，不改变信号
                    #    trend_action_desc_gui = f"趋势:强{('升' if higher_tf_trend_signal == 1 else '降')}确认{sig_dir_for_trend}"
            # 追逐趋势的逻辑 (当原始信号是 Neutral 时)
            elif sig_dir_for_trend == "Neutral" and trend_strategy == 'chase_trend' and is_strong_trend:
                chase_boost = target_config_static.get('trend_chase_confidence_boost', 0.05)
                # 对于UP_ONLY模型，我们关心avg_up_prob_value
                # 对于DOWN_ONLY模型，我们关心avg_down_prob_value
                if target_variable_type == "UP_ONLY" or target_variable_type == "BOTH":
                    if higher_tf_trend_signal == 1 and avg_up_prob_value > (0.5 + chase_boost): # 强升，且UP概率尚可
                        final_signal_for_internal_state = "UP_Chasing"; trend_action_desc_gui = "趋势:强升追多"
                if target_variable_type == "DOWN_ONLY" or target_variable_type == "BOTH": # 注意这里是 if 不是 elif，因为BOTH模型可能两个都看
                    if higher_tf_trend_signal == -1 and avg_down_prob_value > (0.5 + chase_boost): # 强降，且DOWN概率尚可
                        final_signal_for_internal_state = "DOWN_Chasing"; trend_action_desc_gui = "趋势:强降追空"
            if trend_action_desc_gui: filter_reason_details_for_gui += f"\n{trend_action_desc_gui}"


        # --- END OF FILTERING LOGIC PLACEHOLDER ---





        # 根据过滤后的 final_signal_for_internal_state，重新确定 actual_signal_for_sound_and_simulator, prediction_label, prediction_color
        # (您提供的这部分逻辑，确保它在过滤器之后)
        if "UP" in final_signal_for_internal_state and not final_signal_for_internal_state.startswith("Error_"):
            actual_signal_for_sound_and_simulator = "UP"; prediction_label = "上涨 看多" + (" (追涨)" if "_Chasing" in final_signal_for_internal_state else ""); prediction_color = config.UP_COLOR
        elif "DOWN" in final_signal_for_internal_state and not final_signal_for_internal_state.startswith("Error_"):
            actual_signal_for_sound_and_simulator = "DOWN"; prediction_label = "下跌 看空" + (" (追跌)" if "_Chasing" in final_signal_for_internal_state else ""); prediction_color = config.DOWN_COLOR
        elif "Filtered" in final_signal_for_internal_state:
            actual_signal_for_sound_and_simulator = None; prediction_label = f"中性 ({filter_reason_details_for_gui.strip() if filter_reason_details_for_gui else '已过滤'})"; prediction_color = config.NEUTRAL_COLOR
        else:
            actual_signal_for_sound_and_simulator = None; prediction_label = "中性 观望" if final_signal_for_internal_state == "Neutral" else f"错误 ({final_signal_for_internal_state})"; prediction_color = config.NEUTRAL_COLOR if final_signal_for_internal_state == "Neutral" else config.ERROR_COLOR
        
        # --- 构建 details 字符串 ---
        details_model_gui = f"(模型: {model_type_loaded_str})"
        if model_meta: # 确保 model_meta 已加载
            config_suffix_parts_meta = model_meta.get("model_suffix_parts_used_for_run0_artifacts", [])
            suffix_display_gui = ""
            if isinstance(config_suffix_parts_meta, list) and config_suffix_parts_meta: 
                actual_parts_to_join = [s for s in config_suffix_parts_meta if s and isinstance(s, str)] 
                if actual_parts_to_join: suffix_display_gui = f" (Train: {', '.join(actual_parts_to_join)})"
            elif isinstance(config_suffix_parts_meta, str) and config_suffix_parts_meta.strip():
                 suffix_display_gui = f" (Train: {config_suffix_parts_meta.strip()})"
            if suffix_display_gui: details_model_gui += suffix_display_gui
        details = details_model_gui + (filter_reason_details_for_gui if filter_reason_details_for_gui else "")

        # --- 根据预测的最终结果，更新 status_msg_for_gui 和 status_level_final_gui ---
        # (这里的逻辑应该在 try 块的末尾，但在 except 之前，并且在 !return_core_prediction_only 条件内)
        # (因为 return_core_prediction_only 为 True 时，这些详细的 status_msg 不会直接用于该基础模型的GUI状态栏)
        # (但为了简化，我们先在这里确定，如果函数继续执行到信号发送和GUI更新，它们会被使用)
        if final_signal_for_internal_state.startswith("Error_"):
            status_msg_for_gui = f"预测 '{target_name}' 失败 ({final_signal_for_internal_state})"
            status_level_final_gui = "error"
        elif any_successful_model_prediction:
            status_msg_for_gui = f"预测 '{target_name}' 完成"
            if actual_signal_for_sound_and_simulator == "UP": status_msg_for_gui += " (看多)"; status_level_final_gui = config.UP_COLOR
            elif actual_signal_for_sound_and_simulator == "DOWN": status_msg_for_gui += " (看空)"; status_level_final_gui = config.DOWN_COLOR
            elif "Filtered" in final_signal_for_internal_state: status_msg_for_gui += " (已过滤)"; status_level_final_gui = "warning"
            elif final_signal_for_internal_state == "Neutral": status_msg_for_gui += " (中性)"; status_level_final_gui = "success" 
            else: status_level_final_gui = "info"
        else:
            status_msg_for_gui = f"预测 '{target_name}' 未完成/结果未知"
            status_level_final_gui = "warning"
        
    # --- try 块到此结束，下面是 except 块 ---
    except Exception as e_pred_core:
        any_successful_model_prediction = False 
        final_signal_for_internal_state = f"Error_Core_{type(e_pred_core).__name__}"
        model_positive_class_prob = np.nan; avg_up_prob_value = np.nan; avg_down_prob_value = np.nan
        actual_signal_for_sound_and_simulator = None

        print(f"!!! '{target_name}' 在核心预测流程中发生错误: {e_pred_core}")
        traceback.print_exc(limit=1) 
        
        prediction_label = f"错误 ({type(e_pred_core).__name__})"
        details = str(e_pred_core)[:150]
        prediction_color = config.ERROR_COLOR

        if not return_core_prediction_only:
            status_msg_for_gui = f"预测 '{target_name}' 核心流程错误: {type(e_pred_core).__name__}"
            status_level_final_gui = "error"
            if hasattr(gui, 'update_prediction_display') and gui._gui_root and gui._gui_root.winfo_exists():
                error_display_text_main = (f"预测 '{target_name}' 发生错误:\n"
                                           f"时间: {datetime.now(app_timezone_param if app_timezone_param else timezone.utc).strftime('%H:%M:%S %Z')}\n"
                                           f"错误类型: {type(e_pred_core).__name__}\n信息: {details}")
                gui.update_gui_safe(gui.update_prediction_display, target_name, error_display_text_main, prediction_color)
            if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
                 gui.update_gui_safe(gui.update_status, status_msg_for_gui, status_level_final_gui)
        
        if return_core_prediction_only:
            return {"p_favorable": np.nan, "p_up": np.nan, "p_down": np.nan,
                    "internal_signal": final_signal_for_internal_state, "error": True}
        # 对于非核心模式，any_successful_model_prediction 和 final_signal_for_internal_state 已被设为错误状态
        # 函数将在 try...except 结构之后，根据这些状态决定是否继续执行信号发送和最终GUI更新
        # 或者，如果希望异常后直接返回，可以在这里为非核心模式也加上 return：
        # else:
        #     return any_successful_model_prediction, final_signal_for_internal_state
    
    # --- 信号发送决策逻辑 (仅在 not return_core_prediction_only 且核心预测成功时执行) ---
    # 注意：any_successful_model_prediction 的值取决于 try 块是否成功执行到最后或中途因异常退出
    if not return_core_prediction_only and any_successful_model_prediction:
        # 初始化此块内作用域的变量
        should_send_signal_this_time = False 
        trade_amount_to_send_float = 0.0   

        # 获取最新的动态配置
        dynamic_params_current_target_final = dynamic_config_manager.get_target_params(target_name, target_config_static)
        is_target_signal_enabled_dynamically_final = dynamic_params_current_target_final.get("enabled", True)
        is_master_signal_sending_enabled_final = dynamic_config_manager.get_global_param("master_signal_sending_enabled", True)

        if actual_signal_for_sound_and_simulator and \
           is_target_signal_enabled_dynamically_final and \
           is_master_signal_sending_enabled_final:

            prob_for_amount_calc = 0.0
            # avg_up_prob_value 和 avg_down_prob_value 应在 try 块内被正确计算和赋值
            if actual_signal_for_sound_and_simulator == "UP" and not pd.isna(avg_up_prob_value):
                prob_for_amount_calc = avg_up_prob_value
            elif actual_signal_for_sound_and_simulator == "DOWN" and not pd.isna(avg_down_prob_value):
                prob_for_amount_calc = avg_down_prob_value
            
            if prob_for_amount_calc > 0 or target_config_static.get('trade_amount_strategy') == "fixed":
                trade_amount_to_send_float = calculate_trade_amount_from_strategy(
                    target_config_static,
                    actual_signal_for_sound_and_simulator,
                    prob_for_amount_calc
                )

                # 🎯 记录交易开仓到动态胜率跟踪器
                try:
                    from .dynamic_win_rate_tracker import get_dynamic_win_rate_tracker
                    win_rate_tracker = get_dynamic_win_rate_tracker()

                    # 生成交易ID
                    trade_id = f"{target_name}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

                    # 记录交易开仓
                    win_rate_tracker.record_trade_entry(
                        trade_id=trade_id,
                        direction=actual_signal_for_sound_and_simulator,
                        amount=trade_amount_to_send_float,
                        target_name=target_name
                    )

                    print(f"[{target_name}] 已记录交易开仓: {trade_id} ({actual_signal_for_sound_and_simulator}, ${trade_amount_to_send_float:.2f})")

                except Exception as e_trade_record:
                    print(f"[{target_name}] 记录交易开仓失败: {e_trade_record}")
                    # 不影响主流程，继续执行
            
            if trade_amount_to_send_float > 0:
                last_sent_t = global_prediction_state_manager.get_last_signal_sent_time(target_name)
                last_sent_sig_type = global_prediction_state_manager.get_last_signal_type_sent(target_name)
                cooldown_seconds_cfg = getattr(config, 'SIGNAL_SEND_COOLDOWN_SECONDS', SIGNAL_SEND_COOLDOWN_SECONDS)

                if last_sent_sig_type != actual_signal_for_sound_and_simulator or \
                   (time.time() - last_sent_t) >= cooldown_seconds_cfg :
                    should_send_signal_this_time = True
            
            if should_send_signal_this_time:
                # play_signal_alert_sound will internally use global_prediction_state_manager
                play_signal_alert_sound(actual_signal_for_sound_and_simulator)
        
        # --- 记录预测上下文用于失败案例分析 ---
        if should_send_signal_this_time and actual_signal_for_sound_and_simulator:
            # 获取当前特征名称和值
            feature_names_current = []
            feature_values_current = []
            
            if scaled_X_for_model is not None and hasattr(scaler, 'feature_names_in_'):
                feature_names_current = list(scaler.feature_names_in_)
                feature_values_current = scaled_X_for_model[0].tolist()
            
            # 构建信号数据
            signal_data = {
                'signal_type': actual_signal_for_sound_and_simulator,
                'signal_strength': model_positive_class_prob,
                'avg_up_prob': avg_up_prob_value,
                'avg_down_prob': avg_down_prob_value,
                'model_positive_class_prob': model_positive_class_prob,
                'signal_threshold_used': sig_thresh_use,
                'planned_amount': trade_amount_to_send_float,
                'amount_calculation_method': target_config_static.get('trade_amount_strategy', 'unknown')
            }
            
            # 构建市场数据
            market_data = {
                'current_price': get_real_time_price(binance_client, symbol_override=symbol_to_use),
                'last_kline_close': last_kline_close_main
            }
            
            # 构建模型数据
            model_data = {
                'feature_names': feature_names_current,
                'feature_values': feature_values_current,
                'model_type': model_type_loaded_str,
                'ensemble_size': len(loaded_ensemble_models)
            }
            
            # 构建过滤器数据
            filter_data = {
                'trend_signal': higher_tf_trend_signal,
                'trend_strength': higher_tf_trend_strength,
                'trend_status_text': current_trend_status_text,
                'adx_value': getattr(locals(), 'latest_adx', None),
                'pdi_value': getattr(locals(), 'latest_pdi', None),
                'mdi_value': getattr(locals(), 'latest_mdi', None),
                'volatility_level': current_volatility_level,
                'volatility_status_text': current_volatility_status_text,
                'atr_value': getattr(locals(), 'atr_now', None),
                'atr_percent': getattr(locals(), 'atr_pct', None),
                'dynamic_threshold_adjustments': getattr(locals(), 'adj_reasons_gui', []),
                'filter_reasons': filter_reason_details_for_gui
            }
            
            # 记录预测上下文
            log_prediction_context(
                target_name=target_name,
                symbol=symbol_to_use,
                signal_data=signal_data,
                market_data=market_data,
                model_data=model_data,
                filter_data=filter_data
            )

        # --- 实际的信号发送操作 ---
        if should_send_signal_this_time: # 这个 if 块需要和上面的 if 块在同一缩进级别
            target_name_for_comm = f"{target_name}_{symbol_to_use}" # symbol_to_use 需要在函数顶部定义
            print(f"预测系统 [{target_name_for_comm}]: 准备发送信号. 类型: {actual_signal_for_sound_and_simulator}, 金额: {trade_amount_to_send_float:.2f}")

            if SEND_SIGNALS_TO_SIMULATOR:
                _notify_simulator(
                    signal_type=actual_signal_for_sound_and_simulator,
                    target_name_origin=f"{target_name_for_comm}_ToSim",
                    amount=trade_amount_to_send_float,
                    symbol_for_sim=symbol_to_use, 
                    sim_url_param=simulator_actual_url
                )

            SEND_TO_COMMAND_SERVER_ENABLED = getattr(config, 'SEND_TO_COMMAND_SERVER_ENABLED', True)
            if SEND_TO_COMMAND_SERVER_ENABLED:
                payload_cs = {
                    "signal_type": actual_signal_for_sound_and_simulator,
                    "amount": trade_amount_to_send_float,
                    "symbol": symbol_to_use,
                    "target_name": f"{target_name_for_comm}_ToReal"
                }
                try:
                    COMMAND_SERVER_URL_GLOBAL = getattr(config, 'COMMAND_SERVER_URL', "http://127.0.0.1:8080/internal_signal")
                    requests.post(COMMAND_SERVER_URL_GLOBAL, json=payload_cs, timeout=3).raise_for_status()
                    print(f"预测系统 [{symbol_to_use} via {target_name_for_comm}]: 信号成功发送至 CommandServer ({COMMAND_SERVER_URL_GLOBAL})...")
                except Exception as e_cs_send:
                    print(f"!!! 预测系统: 发送信号至 CommandServer ({COMMAND_SERVER_URL_GLOBAL}) 失败: {e_cs_send}")

            global_prediction_state_manager.update_last_signal_sent_time(target_name, time.time())
            global_prediction_state_manager.update_last_signal_type_sent(target_name, actual_signal_for_sound_and_simulator)
            # dynamic_params_current_target_final 变量名可能与上面获取动态配置的变量冲突，确保使用正确的那个
            if dynamic_params_current_target_final.get('enable_initial_conservative_betting', False) and \
               target_config_static.get('trade_amount_strategy') == "kelly_config":
                global_prediction_state_manager.increment_strategy_execution_counter(target_name)

        # --- GUI 更新逻辑 (在信号发送之后，并且仍然在 if not return_core_prediction_only and any_successful_model_prediction 条件内) ---
        # 再次调整 status_msg_for_gui 以反映是否因动态配置而未发送 (如果信号发送决策逻辑没有发送)
        if any_successful_model_prediction and \
           actual_signal_for_sound_and_simulator is not None and \
           not (is_target_signal_enabled_dynamically_final and is_master_signal_sending_enabled_final) and \
           not should_send_signal_this_time: # 添加这个条件，确保是由于开关导致没发送，而不是冷却等
            status_msg_for_gui += " (信号发送被动态禁用)" 
            if status_level_final_gui in ["success", config.UP_COLOR, config.DOWN_COLOR]:
                status_level_final_gui = "info" 
        
        # 构建最终的详细文本显示在 ScrolledText 中
        pred_time_gui_str = datetime.now(app_timezone_param if app_timezone_param else timezone.utc).strftime("%H:%M:%S %Z")
        current_price_val_gui = get_real_time_price(binance_client, symbol_override=symbol_to_use) # symbol_to_use
        price_str_gui = f"${current_price_val_gui:.2f}" if current_price_val_gui is not None else (f"${last_kline_close_main:.2f}(K)" if last_kline_close_main is not None else "$----.--") # last_kline_close_main
        
        result_txt_parts_for_gui = [
            f"预测时间: {pred_time_gui_str}",
            f"当前 {symbol_to_use} 价格: {price_str_gui}",
            current_trend_status_text, 
            current_volatility_status_text 
        ]

        if not (up_prob_display == "N/A" and down_prob_display == "N/A"): # up_prob_display, down_prob_display
            result_txt_parts_for_gui.extend([
                f"\n预测信号: {prediction_label}", 
                f" 模型 P(涨): {up_prob_display}",
                f" 模型 P(跌): {down_prob_display}"
            ])
            if should_send_signal_this_time and trade_amount_to_send_float > 0:
                result_txt_parts_for_gui.append(f" 发送金额: {trade_amount_to_send_float:.2f}")
        else:
            result_txt_parts_for_gui.append(f"\n预测信号: {prediction_label}")

        if details and details.strip(): # details
            result_txt_parts_for_gui.append("\n" + details.strip())
        
        result_text_final_for_gui = "\n".join(filter(None, result_txt_parts_for_gui))

        # 实际的GUI调用
        gui_can_update = False
        current_gui_root_for_update = gui._gui_root if hasattr(gui, '_gui_root') else None
        if hasattr(gui, 'update_prediction_display') and current_gui_root_for_update and hasattr(current_gui_root_for_update, 'winfo_exists'):
            try:
                if current_gui_root_for_update.winfo_exists(): gui_can_update = True
            except tk.TclError: pass 

        if gui_can_update:
            gui.update_gui_safe(gui.update_prediction_display, target_name, result_text_final_for_gui, prediction_color) # prediction_color
            if hasattr(gui, 'update_status'):
                 gui.update_gui_safe(gui.update_status, status_msg_for_gui, status_level_final_gui) # status_msg_for_gui, status_level_final_gui

    # 函数末尾的返回
    if not return_core_prediction_only:
        return any_successful_model_prediction, final_signal_for_internal_state
    else:
        # 这条路径理论上不应该在 try 块成功执行后到达，因为核心模式应该在 try 块内返回。
        # 但如果 any_successful_model_prediction 为 False（例如初始化就是False且try中未成功），则会到这里。
        if any_successful_model_prediction: # 这种情况不应该发生
             print(f"!!! 警告: 意外的执行流 (核心模式但 try 块未返回)。")
             return {"p_favorable": model_positive_class_prob, "p_up": avg_up_prob_value, "p_down": avg_down_prob_value, 
                     "internal_signal": final_signal_for_internal_state, "error": False} # 假设有值
        else: # 如果核心预测本身就失败了（例如初始化后未成功）
             return {"p_favorable": np.nan, "p_up": np.nan, "p_down": np.nan, 
                     "internal_signal": final_signal_for_internal_state if final_signal_for_internal_state else "Error_Unknown_Core_Init", 
                     "error": True}



def calculate_global_market_state(binance_client):
    """
    计算全局市场状态指标，用于元模型特征工程
    返回包含全局趋势和波动率指标的字典
    """
    try:
        global_config = getattr(config, 'GLOBAL_MARKET_STATE_CONFIG', {})
        symbol = global_config.get('symbol', 'BTCUSDT')
        timeframe = global_config.get('timeframe', '1h')
        data_limit = global_config.get('data_fetch_limit', 200)

        # 获取全局市场数据
        df_global = fetch_binance_history(binance_client, symbol, timeframe, limit=data_limit)
        if df_global is None or df_global.empty or len(df_global) < 50:
            return {
                'global_trend_signal': 0,
                'global_trend_strength': 0,
                'global_adx': 0.0,
                'global_pdi': 0.0,
                'global_mdi': 0.0,
                'global_ema_short': 0.0,
                'global_ema_long': 0.0,
                'global_volatility_level': 0,
                'global_atr': 0.0,
                'global_atr_percent': 0.0,
                'global_status': 'data_insufficient'
            }

        # 初始化返回值 (优化EMA特征)
        result = {
            'global_trend_signal': 0,  # -1: 下降, 0: 无明确趋势, 1: 上升
            'global_trend_strength': 0,  # 0: 弱趋势, 1: 强趋势
            'global_adx': 0.0,
            'global_pdi': 0.0,
            'global_mdi': 0.0,
            # 🎯 优化：保留原始EMA特征但降低权重
            'global_ema_short': 0.0,
            'global_ema_long': 0.0,
            # 🎯 新增：高信息量EMA衍生特征
            'global_ema_diff': 0.0,                    # EMA差值 (short - long)
            'global_ema_diff_pct': 0.0,                # EMA差值百分比 ((short-long)/long*100)
            'global_price_ema_distance_pct': 0.0,      # 价格与EMA距离百分比
            'global_ema_slope_short': 0.0,             # 短期EMA斜率
            'global_ema_slope_long': 0.0,              # 长期EMA斜率
            'global_ema_cross_signal': 0,              # EMA交叉信号 (1=金叉, -1=死叉, 0=无交叉)
            'global_ema_divergence': 0.0,              # 价格与EMA背离程度
            'global_volatility_level': 0,  # -1: 高波动, 0: 正常, 1: 低波动
            'global_atr': 0.0,
            'global_atr_percent': 0.0,
            'global_status': 'calculated'
        }

        # 🎯 优化：计算增强的EMA特征集
        ema_short_period = global_config.get('trend_ema_short_period', 21)
        ema_long_period = global_config.get('trend_ema_long_period', 50)
        ema_slope_period = global_config.get('ema_slope_period', 5)  # EMA斜率计算周期

        # 验证数据长度是否足够计算EMA和衍生特征
        min_required_length = max(ema_short_period, ema_long_period) * 2 + ema_slope_period
        if len(df_global) >= min_required_length:
            try:
                close_prices = df_global['close']
                current_price = float(close_prices.iloc[-1])

                # 计算基础EMA
                ema_short = pta.ema(close_prices, length=ema_short_period)
                ema_long = pta.ema(close_prices, length=ema_long_period)

                if ema_short is not None and ema_long is not None and not ema_short.empty and not ema_long.empty:
                    # 获取最新的非NaN EMA值
                    latest_ema_short = ema_short.dropna().iloc[-1] if not ema_short.dropna().empty else 0.0
                    latest_ema_long = ema_long.dropna().iloc[-1] if not ema_long.dropna().empty else 0.0

                    # 基础EMA特征
                    result['global_ema_short'] = float(latest_ema_short)
                    result['global_ema_long'] = float(latest_ema_long)

                    # 🎯 新增：高信息量EMA衍生特征
                    if latest_ema_long > 0:
                        # 1. EMA差值和百分比
                        ema_diff = latest_ema_short - latest_ema_long
                        result['global_ema_diff'] = float(ema_diff)
                        result['global_ema_diff_pct'] = float((ema_diff / latest_ema_long) * 100)

                        # 2. 价格与EMA距离百分比 (使用短期EMA作为参考)
                        result['global_price_ema_distance_pct'] = float((current_price - latest_ema_short) / latest_ema_short * 100)

                        # 3. EMA斜率计算 (变化率)
                        if len(ema_short.dropna()) >= ema_slope_period and len(ema_long.dropna()) >= ema_slope_period:
                            ema_short_clean = ema_short.dropna()
                            ema_long_clean = ema_long.dropna()

                            # 计算斜率 (最近N期的平均变化率)
                            short_slope = (ema_short_clean.iloc[-1] - ema_short_clean.iloc[-ema_slope_period]) / ema_slope_period
                            long_slope = (ema_long_clean.iloc[-1] - ema_long_clean.iloc[-ema_slope_period]) / ema_slope_period

                            result['global_ema_slope_short'] = float(short_slope / latest_ema_short * 100)  # 标准化为百分比
                            result['global_ema_slope_long'] = float(long_slope / latest_ema_long * 100)

                        # 4. EMA交叉信号检测
                        if len(ema_short.dropna()) >= 2 and len(ema_long.dropna()) >= 2:
                            ema_short_clean = ema_short.dropna()
                            ema_long_clean = ema_long.dropna()

                            # 当前和前一期的EMA关系
                            current_above = ema_short_clean.iloc[-1] > ema_long_clean.iloc[-1]
                            previous_above = ema_short_clean.iloc[-2] > ema_long_clean.iloc[-2]

                            if current_above and not previous_above:
                                result['global_ema_cross_signal'] = 1  # 金叉
                            elif not current_above and previous_above:
                                result['global_ema_cross_signal'] = -1  # 死叉
                            else:
                                result['global_ema_cross_signal'] = 0  # 无交叉

                        # 5. 价格与EMA背离程度
                        # 计算价格趋势与EMA趋势的背离
                        if len(close_prices) >= ema_slope_period:
                            price_slope = (close_prices.iloc[-1] - close_prices.iloc[-ema_slope_period]) / ema_slope_period
                            price_slope_pct = price_slope / close_prices.iloc[-ema_slope_period] * 100
                            ema_slope_pct = result['global_ema_slope_short']

                            # 背离程度 = 价格斜率 - EMA斜率
                            result['global_ema_divergence'] = float(price_slope_pct - ema_slope_pct)

                    # 调试输出 (仅在启用时)
                    if getattr(config, 'EMA_DEBUG_VERBOSE', False):
                        print(f"    [GlobalEMA] 数据长度: {len(df_global)}")
                        print(f"    [GlobalEMA] EMA短期: {latest_ema_short:.2f}, EMA长期: {latest_ema_long:.2f}")
                        print(f"    [GlobalEMA] EMA差值: {result['global_ema_diff']:.2f}, 差值%: {result['global_ema_diff_pct']:.2f}%")
                        print(f"    [GlobalEMA] 价格距离%: {result['global_price_ema_distance_pct']:.2f}%")
                        print(f"    [GlobalEMA] 交叉信号: {result['global_ema_cross_signal']}, 背离: {result['global_ema_divergence']:.2f}")
                else:
                    print(f"    ⚠️ [GlobalEMA] EMA计算失败，使用默认值")
            except Exception as e:
                print(f"    ❌ [GlobalEMA] EMA计算异常: {e}")
        else:
            print(f"    ⚠️ [GlobalEMA] 数据不足: {len(df_global)} < {min_required_length}")

        # 计算全局趋势指标
        trend_indicator_type = global_config.get('trend_indicator_type', 'ema_cross')
        if trend_indicator_type == 'adx':
            adx_period = global_config.get('trend_adx_period', 14)
            adx_strength_threshold = global_config.get('trend_adx_strength_threshold', 25)
            adx_threshold = global_config.get('trend_adx_threshold', 20)

            adx_df = pta.adx(df_global['high'], df_global['low'], df_global['close'],
                           length=adx_period, lensig=adx_period)
            if adx_df is not None and not adx_df.empty:
                adx_col, pdi_col, mdi_col = f'ADX_{adx_period}', f'DMP_{adx_period}', f'DMN_{adx_period}'
                if all(c in adx_df.columns for c in [adx_col, pdi_col, mdi_col]):
                    result['global_adx'] = adx_df[adx_col].iloc[-1]
                    result['global_pdi'] = adx_df[pdi_col].iloc[-1]
                    result['global_mdi'] = adx_df[mdi_col].iloc[-1]

                    if not any(pd.isna(v) for v in [result['global_adx'], result['global_pdi'], result['global_mdi']]):
                        if result['global_adx'] > adx_threshold:
                            result['global_trend_signal'] = 1 if result['global_pdi'] > result['global_mdi'] else -1
                        if result['global_adx'] > adx_strength_threshold and result['global_trend_signal'] != 0:
                            result['global_trend_strength'] = 1

        elif trend_indicator_type == 'ema_cross':
            # 使用已计算的EMA值来确定趋势信号
            if result['global_ema_short'] > 0 and result['global_ema_long'] > 0:
                if result['global_ema_short'] > result['global_ema_long']:
                    result['global_trend_signal'] = 1
                elif result['global_ema_short'] < result['global_ema_long']:
                    result['global_trend_signal'] = -1

                # 计算趋势强度 (基于EMA差值的百分比)
                ema_diff_percent = abs(result['global_ema_short'] - result['global_ema_long']) / result['global_ema_long'] * 100
                if ema_diff_percent > 2.0:  # 2%以上的差值认为是强趋势
                    result['global_trend_strength'] = 1

        # 计算全局波动率指标
        atr_period = global_config.get('volatility_atr_period', 14)
        min_atr_percent = global_config.get('volatility_min_atr_percent', 0.08)
        max_atr_percent = global_config.get('volatility_max_atr_percent', 1.5)

        atr_series = pta.atr(df_global['high'], df_global['low'], df_global['close'], length=atr_period)
        if atr_series is not None and not atr_series.empty and not pd.isna(atr_series.iloc[-1]):
            result['global_atr'] = atr_series.iloc[-1]
            close_price = df_global['close'].iloc[-1]
            result['global_atr_percent'] = (result['global_atr'] / close_price) * 100 if close_price > 0 else 0.0

            if result['global_atr_percent'] < min_atr_percent:
                result['global_volatility_level'] = 1  # 低波动
            elif result['global_atr_percent'] > max_atr_percent:
                result['global_volatility_level'] = -1  # 高波动
            else:
                result['global_volatility_level'] = 0  # 正常波动

        return result

    except Exception as e:
        print(f"!!! 计算全局市场状态时出错: {e}")
        return {
            'global_trend_signal': 0,
            'global_trend_strength': 0,
            'global_adx': 0.0,
            'global_pdi': 0.0,
            'global_mdi': 0.0,
            'global_ema_short': 0.0,
            'global_ema_long': 0.0,
            'global_volatility_level': 0,
            'global_atr': 0.0,
            'global_atr_percent': 0.0,
            'global_status': 'error'
        }


def run_meta_prediction_for_current_trigger(binance_client, app_timezone_param, simulator_actual_url=None):
    global dynamic_config_manager, last_signal_sent_time_per_target, last_signal_type_sent_per_target, strategy_execution_counters

    print("\n--- 开始元模型预测周期 ---")
    
    if not global_prediction_state_manager.is_meta_model_loaded_successfully():
        if not load_meta_model_if_needed(): # load_meta_model_if_needed 应该在 prediction.py 中
            print("!!! 元模型预测中止: 元模型未能加载。")
            if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
                gui.update_gui_safe(gui.update_status, "元模型加载失败，预测中止", "error")
            return False, "Error_Meta_Load"

    base_model_probas = {} 
    base_model_signals_internal = {} 
    base_model_inputs_for_meta = {} 
    base_model_display_texts = {}   
    all_core_infos_from_bases = {} # <--- 初始化正确
    all_base_models_succeeded_for_meta_input = True

    input_features_config = getattr(config, 'META_MODEL_INPUT_FEATURES_CONFIG', [])
    if not input_features_config:
        print(f"!!! 元模型配置错误: config.META_MODEL_INPUT_FEATURES_CONFIG 未定义或为空。")
        return False, "Error_Meta_Input_Config"

    # 提取所有需要的基础模型名称，避免重复获取
    unique_base_model_names_needed = list(set([item['base_model_name'] for item in input_features_config if 'base_model_name' in item])) # Ensure key exists

    # 步骤1: 获取所有需要的基础模型的核心预测信息并构建其GUI文本
    for base_model_name in unique_base_model_names_needed:
        display_text_for_base = f"基础模型 '{base_model_name}':\n"
        color_for_base_gui = config.NEUTRAL_COLOR
        try:
            base_cfg = config.get_target_config(base_model_name)
            
            core_pred_info = run_prediction_cycle_for_target(
                base_cfg, binance_client, app_timezone_param, return_core_prediction_only=True
            )
            


            # --- 将获取到的 core_pred_info 存储到 all_core_infos_from_bases ---
            all_core_infos_from_bases[base_model_name] = core_pred_info  # <--- *** 添加这一行 ***
            # --- 存储结束 ---

            if core_pred_info.get("error", True): # 如果核心预测出错
                signal_to_log = core_pred_info.get("internal_signal", "未知错误")
                display_text_for_base += f"  核心预测失败: {signal_to_log}"
                color_for_base_gui = config.ERROR_COLOR
                base_model_inputs_for_meta[base_model_name] = np.nan 
                all_base_models_succeeded_for_meta_input = False
            else: # 核心预测成功
                p_favorable = core_pred_info.get("p_favorable", np.nan)
                # p_up = core_pred_info.get("p_up", np.nan) # 这行不需要，因为下面直接用 p_up_val
                # p_down = core_pred_info.get("p_down", np.nan) # 这行不需要
                # internal_signal = core_pred_info.get("internal_signal", "N/A") # 这行不需要

                base_model_inputs_for_meta[base_model_name] = p_favorable 

                display_text_for_base += f"  时间: {datetime.now(app_timezone_param if app_timezone_param else timezone.utc).strftime('%H:%M:%S')}\n" # 您有两行这个，可以保留一个
                
                # 从 core_pred_info 中获取 p_favorable, p_up_val, p_down_val, internal_signal
                p_favorable_val_disp = core_pred_info.get("p_favorable", np.nan) # 重命名以避免与上面的 p_favorable 混淆
                p_up_val = core_pred_info.get("p_up", np.nan) 
                p_down_val = core_pred_info.get("p_down", np.nan) 
                internal_signal_disp = core_pred_info.get("internal_signal", "N/A") # 重命名

                # 对 p_favorable_val_disp 进行格式化
                p_favorable_str = f"{p_favorable_val_disp:.3f}" if not pd.isna(p_favorable_val_disp) else "N/A"
                display_text_for_base += f"  P(有利方向): {p_favorable_str}\n"

                p_up_display_str = f"{p_up_val:.3f}" if not pd.isna(p_up_val) else "N/A"
                p_down_display_str = f"{p_down_val:.3f}" if not pd.isna(p_down_val) else "N/A"
                
                display_text_for_base += f"  P(涨): {p_up_display_str}\n"
                display_text_for_base += f"  P(跌): {p_down_display_str}\n"
                
                display_text_for_base += f"  初步信号: {internal_signal_disp}"
            
            base_model_display_texts[base_model_name] = (display_text_for_base, color_for_base_gui)

        except Exception as e_base_pred_loop:
            display_text_for_base += f"  获取预测时发生严重错误: {e_base_pred_loop}"
            base_model_display_texts[base_model_name] = (display_text_for_base, config.ERROR_COLOR)
            base_model_inputs_for_meta[base_model_name] = np.nan
            # 即使在异常中，也为 all_core_infos_from_bases 添加一个错误条目
            all_core_infos_from_bases[base_model_name] = {
                "p_favorable": np.nan, "p_up": np.nan, "p_down": np.nan,
                "internal_signal": f"Error_Loop_{type(e_base_pred_loop).__name__}", 
                "error": True
            }
            all_base_models_succeeded_for_meta_input = False
            print(f"!!! 获取基础模型 '{base_model_name}' 核心预测时出错: {e_base_pred_loop}")
            traceback.print_exc(limit=1)


    # 步骤1.1: 计算全局市场状态
    print("计算全局市场状态...")
    global_market_state = calculate_global_market_state(binance_client)

    # 步骤1.2: 更新基础模型的GUI显示区域
    if hasattr(gui, 'update_prediction_display') and gui._gui_root and gui._gui_root.winfo_exists():
        for model_name, (text_content, color) in base_model_display_texts.items():
            gui.update_gui_safe(gui.update_prediction_display, model_name, text_content, color)

    if not all_base_models_succeeded_for_meta_input:
        print("!!! 元模型中止: 一个或多个基础模型未能成功提供核心预测。")
        if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
             gui.update_gui_safe(gui.update_status, "基础模型核心预测失败，元模型中止", "error")
        return False, "Error_Base_Core_Pred_Fail"



    # 步骤2: 准备元模型的输入特征 (确保顺序与训练时一致)
    meta_input_data_for_df = {}
    # 从状态管理器获取元模型训练时使用的特征名列表，这将决定DataFrame的列顺序
    trained_meta_feature_names = global_prediction_state_manager.get_meta_model_feature_names()
    if not trained_meta_feature_names:
        print("!!! 元模型错误: meta_model_feature_names 未从状态管理器加载。")
        return False, "Error_Meta_Feat_Load"

    # 🎯 构建丰富化的元模型特征（包括基础概率和上下文特征）
    for ft_config in input_features_config:
        base_name = ft_config.get('base_model_name')
        prob_type_key = ft_config.get('prob_type') # Changed from 'probability_type'
        meta_feature_col_name = ft_config.get('meta_feature_name') # Changed from 'feature_name_in_meta_model'

        if not all([base_name, prob_type_key, meta_feature_col_name]):
            print(f"!!! 元模型输入配置警告: 跳过不完整的特征配置项: {ft_config}")
            continue

        core_info = all_core_infos_from_bases.get(base_name)
        if core_info and not core_info.get("error", True):
            # 🎯 处理上下文特征（嵌套字典访问）
            if prob_type_key.startswith('context_features.'):
                context_key = prob_type_key.split('.', 1)[1]  # 提取context_features后面的键名
                context_features = core_info.get('context_features', {})
                feature_val = context_features.get(context_key, np.nan)

                if pd.isna(feature_val):
                    # 为不同类型的上下文特征设置合适的默认值
                    if context_key == 'atr_percent':
                        default_val = 0.5  # ATR百分比默认值
                    elif context_key == 'adx_value':
                        default_val = 25.0  # ADX默认值（中性趋势强度）
                    elif context_key == 'prediction_confidence':
                        default_val = 0.0  # 置信度默认值（无置信度）
                    elif context_key == 'rsi_value':
                        default_val = 50.0  # RSI默认值（中性）
                    elif context_key == 'volume_ratio':
                        default_val = 1.0  # 成交量比率默认值（正常）
                    elif context_key == 'price_change_1p':
                        default_val = 0.0  # 价格变化默认值（无变化）
                    else:
                        default_val = 0.0  # 其他特征默认值

                    print(f"!!! 元模型输入警告: 基础模型 '{base_name}' 的上下文特征 '{context_key}' 为NaN。使用默认值{default_val}代替特征 '{meta_feature_col_name}'。")
                    meta_input_data_for_df[meta_feature_col_name] = default_val
                else:
                    meta_input_data_for_df[meta_feature_col_name] = float(feature_val)

            # 🎯 处理传统概率特征
            else:
                proba_val = core_info.get(prob_type_key, np.nan)
                if pd.isna(proba_val):
                    print(f"!!! 元模型输入警告: 基础模型 '{base_name}' 的概率类型 '{prob_type_key}' 为NaN。使用0.5代替特征 '{meta_feature_col_name}'。")
                    meta_input_data_for_df[meta_feature_col_name] = 0.5 # Default fill value
                else:
                    meta_input_data_for_df[meta_feature_col_name] = float(proba_val)
        else:
            # 基础模型出错时的默认值处理
            if prob_type_key.startswith('context_features.'):
                context_key = prob_type_key.split('.', 1)[1]
                if context_key == 'atr_percent':
                    default_val = 0.5
                elif context_key == 'adx_value':
                    default_val = 25.0
                elif context_key == 'prediction_confidence':
                    default_val = 0.0
                elif context_key == 'rsi_value':
                    default_val = 50.0
                elif context_key == 'volume_ratio':
                    default_val = 1.0
                elif context_key == 'price_change_1p':
                    default_val = 0.0
                else:
                    default_val = 0.0
                meta_input_data_for_df[meta_feature_col_name] = default_val
            else:
                meta_input_data_for_df[meta_feature_col_name] = 0.5 # Default for probability features

            print(f"!!! 元模型输入错误: 基础模型 '{base_name}' 的核心信息缺失或错误。使用默认值代替特征 '{meta_feature_col_name}'。")

    # 添加趋势和波动率特征 (方案一：将基础模型的趋势/波动率信息作为元模型特征)
    # 从基础模型的核心信息中提取趋势和波动率指标
    for base_model_name, core_info in all_core_infos_from_bases.items():
        if core_info and not core_info.get("error", True):
            # 添加基础模型特定的趋势特征
            meta_input_data_for_df[f'trend_signal_{base_model_name}'] = core_info.get('trend_signal', 0)
            meta_input_data_for_df[f'trend_strength_{base_model_name}'] = core_info.get('trend_strength', 0)
            meta_input_data_for_df[f'adx_value_{base_model_name}'] = core_info.get('adx_value', 0.0)
            meta_input_data_for_df[f'volatility_level_{base_model_name}'] = core_info.get('volatility_level', 0)
            meta_input_data_for_df[f'atr_percent_{base_model_name}'] = core_info.get('atr_percent', 0.0)
        else:
            # 如果基础模型出错，使用默认值
            meta_input_data_for_df[f'trend_signal_{base_model_name}'] = 0
            meta_input_data_for_df[f'trend_strength_{base_model_name}'] = 0
            meta_input_data_for_df[f'adx_value_{base_model_name}'] = 0.0
            meta_input_data_for_df[f'volatility_level_{base_model_name}'] = 0
            meta_input_data_for_df[f'atr_percent_{base_model_name}'] = 0.0

    # 🎯 新增：计算基础模型间的分歧度特征
    feature_engineering_config = getattr(config, 'META_MODEL_FEATURE_ENGINEERING_CONFIG', {})
    if feature_engineering_config.get('enable_model_divergence', True):
        # 计算基础模型间的概率分歧度
        up_probs = []
        down_probs = []
        favorable_probs = []

        for base_name, core_info in all_core_infos_from_bases.items():
            if core_info and not core_info.get("error", True):
                p_up = core_info.get("p_up", np.nan)
                p_down = core_info.get("p_down", np.nan)
                p_favorable = core_info.get("p_favorable", np.nan)

                if not pd.isna(p_up):
                    up_probs.append(p_up)
                if not pd.isna(p_down):
                    down_probs.append(p_down)
                if not pd.isna(p_favorable):
                    favorable_probs.append(p_favorable)

        # 计算分歧度指标
        if len(up_probs) >= 2:
            meta_input_data_for_df['model_divergence_up'] = np.std(up_probs)  # 上涨概率的标准差
        else:
            meta_input_data_for_df['model_divergence_up'] = 0.0

        if len(down_probs) >= 2:
            meta_input_data_for_df['model_divergence_down'] = np.std(down_probs)  # 下跌概率的标准差
        else:
            meta_input_data_for_df['model_divergence_down'] = 0.0

        if len(favorable_probs) >= 2:
            meta_input_data_for_df['model_divergence_favorable'] = np.std(favorable_probs)  # 有利概率的标准差
        else:
            meta_input_data_for_df['model_divergence_favorable'] = 0.0

        # 计算模型间的最大概率差异
        if len(up_probs) >= 2:
            meta_input_data_for_df['model_max_diff_up'] = max(up_probs) - min(up_probs)
        else:
            meta_input_data_for_df['model_max_diff_up'] = 0.0

        if len(down_probs) >= 2:
            meta_input_data_for_df['model_max_diff_down'] = max(down_probs) - min(down_probs)
        else:
            meta_input_data_for_df['model_max_diff_down'] = 0.0

    # 🎯 新增：计算综合置信度特征
    if feature_engineering_config.get('enable_confidence_features', True):
        confidence_scores = []
        for base_name, core_info in all_core_infos_from_bases.items():
            if core_info and not core_info.get("error", True):
                context_features = core_info.get('context_features', {})
                confidence = context_features.get('prediction_confidence', 0.0)
                if not pd.isna(confidence):
                    confidence_scores.append(confidence)

        if confidence_scores:
            meta_input_data_for_df['avg_model_confidence'] = np.mean(confidence_scores)  # 平均置信度
            meta_input_data_for_df['max_model_confidence'] = np.max(confidence_scores)   # 最大置信度
            meta_input_data_for_df['min_model_confidence'] = np.min(confidence_scores)   # 最小置信度
            meta_input_data_for_df['confidence_spread'] = np.max(confidence_scores) - np.min(confidence_scores)  # 置信度差异
        else:
            meta_input_data_for_df['avg_model_confidence'] = 0.0
            meta_input_data_for_df['max_model_confidence'] = 0.0
            meta_input_data_for_df['min_model_confidence'] = 0.0
            meta_input_data_for_df['confidence_spread'] = 0.0

    # 添加全局市场状态特征
    for key, value in global_market_state.items():
        if key != 'global_status':  # 排除状态字符串
            meta_input_data_for_df[key] = value

    # 应用实时特征工程 (与训练时保持一致)
    meta_input_data_for_df = apply_realtime_meta_feature_engineering(meta_input_data_for_df, trained_meta_feature_names)

    # 按照元模型训练时的特征顺序构建DataFrame
    # 如果 trained_meta_feature_names 中有的特征在 meta_input_data_for_df 中没有（例如配置错误），则填充0.5
    final_meta_input_list_ordered = [meta_input_data_for_df.get(name, 0.5) for name in trained_meta_feature_names]

    meta_input_X = pd.DataFrame([final_meta_input_list_ordered], columns=trained_meta_feature_names)
    print(f"  元模型输入DataFrame (meta_input_X) 构建完成，形状: {meta_input_X.shape}, 列名: {list(meta_input_X.columns)}")
    # print(f"  元模型输入数据: {meta_input_X.iloc[0].to_dict()}")
    
    # 步骤3: 元模型前处理 (基于置信度的中性判断)
    pre_filter_up_cfg = getattr(config, 'META_MODEL_PRE_FILTER_UP_CONFIG', None)
    pre_filter_down_cfg = getattr(config, 'META_MODEL_PRE_FILTER_DOWN_CONFIG', None)

    prob_for_filter_up = 0.0 # 默认值，如果配置缺失或无法获取概率
    prob_for_filter_down = 0.0
    conf_thresh_up = 0.55 # 默认阈值
    conf_thresh_down = 0.55

    if pre_filter_up_cfg:
        base_name_up = pre_filter_up_cfg.get('base_model_name')
        prob_type_key_up = pre_filter_up_cfg.get('prob_type') # Changed from 'probability_type'
        conf_thresh_up = pre_filter_up_cfg.get('threshold', 0.55) # Default if missing
        if base_name_up and prob_type_key_up:
            core_info_up = all_core_infos_from_bases.get(base_name_up)
            if core_info_up and not core_info_up.get("error", True):
                prob_for_filter_up = core_info_up.get(prob_type_key_up, 0.0)
            else:
                print(f"!!! 元模型预过滤警告: 无法获取用于UP过滤的基础模型 '{base_name_up}' 的概率 '{prob_type_key_up}'。将使用默认值0.0")
                prob_for_filter_up = 0.0 # Default if base model info error
        else:
            print(f"!!! 元模型预过滤配置错误: META_MODEL_PRE_FILTER_UP_CONFIG 中的 base_model_name 或 prob_type 缺失。将使用默认值0.0")
            prob_for_filter_up = 0.0 # Default if config keys missing
    else:
        print("!!! 元模型预过滤配置警告: META_MODEL_PRE_FILTER_UP_CONFIG 未定义。将使用默认值0.0")
        prob_for_filter_up = 0.0 # Default if whole config missing

    if pre_filter_down_cfg:
        base_name_down = pre_filter_down_cfg.get('base_model_name')
        prob_type_key_down = pre_filter_down_cfg.get('prob_type') # Changed from 'probability_type'
        conf_thresh_down = pre_filter_down_cfg.get('threshold', 0.55) # Default if missing
        if base_name_down and prob_type_key_down:
            core_info_down = all_core_infos_from_bases.get(base_name_down)
            if core_info_down and not core_info_down.get("error", True):
                prob_for_filter_down = core_info_down.get(prob_type_key_down, 0.0)
            else:
                print(f"!!! 元模型预过滤警告: 无法获取用于DOWN过滤的基础模型 '{base_name_down}' 的概率 '{prob_type_key_down}'。将使用默认值0.0")
                prob_for_filter_down = 0.0 # Default if base model info error
        else:
            print(f"!!! 元模型预过滤配置错误: META_MODEL_PRE_FILTER_DOWN_CONFIG 中的 base_model_name 或 prob_type 缺失。将使用默认值0.0")
            prob_for_filter_down = 0.0 # Default if config keys missing
    else:
        print("!!! 元模型预过滤配置警告: META_MODEL_PRE_FILTER_DOWN_CONFIG 未定义。将使用默认值0.0")
        prob_for_filter_down = 0.0 # Default if whole config missing


    final_meta_signal = "Neutral_PreFiltered"; prediction_label_meta = "中性 (预过滤)"; prediction_color_meta = config.NEUTRAL_COLOR
    meta_model_decision_probas = [0.33, 0.33, 0.34] # 默认大致均匀的概率 P(D), P(U), P(N)

    meta_model_virtual_target_name = getattr(config, 'META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS', "MetaModel_BTC_15m")
    meta_dynamic_params = dynamic_config_manager.get_target_params(meta_model_virtual_target_name, getattr(config, 'META_MODEL_STATIC_KELLY_CONFIG', {}))
    is_meta_signal_dynamically_enabled = meta_dynamic_params.get("enabled", True)

    # 🎯 移除重复的决策逻辑，确保 _make_intelligent_meta_decision 成为唯一决策出口
    # 预过滤逻辑将在后续的统一决策流程中处理

    # 🎯 信号发送逻辑将在智能决策完成后统一处理


    # --- GUI 更新 ---
    # 使用之前在步骤3中获取的 prob_for_filter_up, prob_for_filter_down, conf_thresh_up, conf_thresh_down
    # 这些变量已经考虑了配置缺失或概率获取失败的情况
    prob_up_m1_for_filter = prob_for_filter_up
    prob_down_m2_for_filter = prob_for_filter_down
    # conf_thresh_up 和 conf_thresh_down 已经在步骤3中设置好了


    pre_filter_triggered_flag = False # 用于记录预过滤是否触发
    # 初始化元模型相关的变量，它们可能在预过滤时使用默认值，或在元模型预测后被更新
    final_meta_signal = "Error_Meta_Signal_Init" 
    prediction_label_meta = "等待元模型决策..."
    meta_model_decision_probas = [1/3, 1/3, 1/3] # 一个中性的初始分布

    if pd.isna(prob_up_m1_for_filter) or pd.isna(prob_down_m2_for_filter):
        print(f"  元模型预处理: 基础概率存在NaN (P_UP_M1={prob_up_m1_for_filter}, P_DOWN_M2={prob_down_m2_for_filter})，结果 -> 中性 (概率获取问题)")
        final_meta_signal = "Neutral_BaseNaN"
        prediction_label_meta = "中性 (基础概率NaN)"
        # meta_model_decision_probas 保持初始值或设为更明确的中性默认值
        pre_filter_triggered_flag = True
    elif prob_up_m1_for_filter < conf_thresh_up and prob_down_m2_for_filter < conf_thresh_down:
        print(f"  元模型预过滤: P(UP)_M1 ({prob_up_m1_for_filter:.3f}) < {conf_thresh_up} AND P(DOWN)_M2 ({prob_down_m2_for_filter:.3f}) < {conf_thresh_down}. 结果 -> 中性。")
        final_meta_signal = "Neutral_PreFiltered"
        prediction_label_meta = "中性 (预过滤)"
        # meta_model_decision_probas 保持初始值或设为更明确的中性默认值
        pre_filter_triggered_flag = True
    else:
        # 预过滤条件不满足，将调用元模型
        print(f"  元模型预过滤判断: P(UP)_M1 ({prob_up_m1_for_filter:.3f}) 或 P(DOWN)_M2 ({prob_down_m2_for_filter:.3f}) 未同时低于阈值。传递给元模型。")
        pre_filter_triggered_flag = False # 明确未触发
        try:
            current_meta_model_instance = global_prediction_state_manager.get_meta_model_instance()
            if not current_meta_model_instance:
                print("!!! 元模型错误: meta_model_instance 未从状态管理器加载 (重复检查点)。")
                # 保留之前的错误处理逻辑或根据需要调整
                final_meta_signal = f"Error_MetaInstLoad2"
                prediction_label_meta = f"错误(元模型实例NULL2)"
                prediction_color_meta = config.ERROR_COLOR
                meta_model_decision_probas = [1/3,1/3,1/3] # 确保定义
            else:
                meta_pred_class = current_meta_model_instance.predict(meta_input_X)[0]
                meta_model_decision_probas = current_meta_model_instance.predict_proba(meta_input_X)[0]
            
            # 使用智能决策逻辑替代简单的argmax
            initial_meta_signal, initial_prediction_label, initial_prediction_color = _make_intelligent_meta_decision(
                meta_model_decision_probas, meta_pred_class
            )
            print(f"  元模型预测: 原始类别={meta_pred_class}, 概率分布={meta_model_decision_probas}")
            print(f"  智能决策结果: {initial_meta_signal}, 标签: {initial_prediction_label}")

            # 🎯 新增：应用动态交易过滤器 - 解决"高波动盘整"问题
            try:
                from .dynamic_trading_filter import DynamicTradingFilter

                # 创建过滤器实例
                trading_filter = DynamicTradingFilter()

                # 获取全局市场状态
                global_market_state = calculate_global_market_state(binance_client)

                # 应用过滤器
                filter_result = trading_filter.apply_filter(
                    signal=initial_meta_signal.replace("_Meta", ""),  # 移除后缀
                    signal_probabilities=list(meta_model_decision_probas),
                    global_market_data=global_market_state,
                    meta_model_probabilities=list(meta_model_decision_probas)
                )

                # 使用过滤后的结果
                if filter_result['should_trade']:
                    final_meta_signal = initial_meta_signal
                    prediction_label_meta = initial_prediction_label
                    prediction_color_meta = initial_prediction_color

                    # 如果置信度被调整，更新标签
                    if filter_result['confidence_adjustment'] < 1.0:
                        confidence_pct = filter_result['confidence_adjustment'] * 100
                        prediction_label_meta += f" (置信度调整:{confidence_pct:.0f}%)"
                        print(f"  🎯 元模型过滤器: 置信度调整 {confidence_pct:.0f}%")
                else:
                    # 信号被过滤，强制为中性
                    final_meta_signal = filter_result['filtered_signal']
                    prediction_label_meta = f"中性 (过滤: {', '.join(filter_result['filter_reasons'][:2])})"
                    prediction_color_meta = config.NEUTRAL_COLOR

                    print(f"  🚨 元模型过滤器: 信号被阻止")
                    print(f"    原始信号: {initial_meta_signal}")
                    print(f"    过滤原因: {', '.join(filter_result['filter_reasons'])}")

                    # 显示市场状态信息
                    market_analysis = filter_result.get('market_analysis', {})
                    if market_analysis:
                        market_regime = market_analysis.get('market_regime', {})
                        if hasattr(market_regime, 'value'):
                            print(f"    市场状态: {market_regime.value}")
                        print(f"    危险评分: {market_analysis.get('danger_score', 0):.2f}")

            except ImportError:
                print("  ⚠️ 动态交易过滤器不可用，使用原始信号")
                final_meta_signal = initial_meta_signal
                prediction_label_meta = initial_prediction_label
                prediction_color_meta = initial_prediction_color
            except Exception as e_filter:
                print(f"  ❌ 动态交易过滤器失败: {e_filter}")
                final_meta_signal = initial_meta_signal
                prediction_label_meta = initial_prediction_label
                prediction_color_meta = initial_prediction_color
        except Exception as e_meta_predict:
            print(f"!!! 元模型实时预测时出错: {e_meta_predict}")
            final_meta_signal = f"Error_MetaPredict_{type(e_meta_predict).__name__}"
            prediction_label_meta = f"错误(元模型: {type(e_meta_predict).__name__})"
            prediction_color_meta = config.ERROR_COLOR
            meta_model_decision_probas = [1/3, 1/3, 1/3] # 错误时重置为平均概率

    # --- 信号发送决策逻辑 (使用上面确定的 final_meta_signal 和 meta_model_decision_probas) ---
    actual_signal_for_sound_and_simulator = None
    if "UP_Meta" in final_meta_signal: actual_signal_for_sound_and_simulator = "UP"
    elif "DOWN_Meta" in final_meta_signal: actual_signal_for_sound_and_simulator = "DOWN"
    
    should_send_signal_this_time_meta = False # 初始化
    trade_amount_to_send_float = 0.0       # 初始化

    # 🎯 获取元模型的动态参数和静态配置
    meta_model_virtual_target_name = getattr(config, 'META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS', "MetaModel_BTC_15m")
    meta_dynamic_params = dynamic_config_manager.get_target_params(meta_model_virtual_target_name, getattr(config, 'META_MODEL_STATIC_KELLY_CONFIG', {}))
    is_meta_signal_dynamically_enabled = meta_dynamic_params.get("enabled", True)
    is_master_signal_sending_enabled = dynamic_config_manager.get_global_param("master_signal_sending_enabled", True)

    # 🎯 完整的信号发送决策逻辑（基于智能决策的结果）
    if actual_signal_for_sound_and_simulator and is_master_signal_sending_enabled and is_meta_signal_dynamically_enabled:
        # --- 使用凯利公式计算元模型信号的金额 ---
        prob_for_meta_amount_calc = 0.0
        if actual_signal_for_sound_and_simulator == "UP":
            prob_for_meta_amount_calc = meta_model_decision_probas[1] # P(元模型预测上涨)
        elif actual_signal_for_sound_and_simulator == "DOWN":
            prob_for_meta_amount_calc = meta_model_decision_probas[0] # P(元模型预测下跌)

        # 调用 calculate_trade_amount_from_strategy
        meta_static_config_for_kelly = getattr(config, 'META_MODEL_STATIC_KELLY_CONFIG', {})

        if meta_static_config_for_kelly.get("trade_amount_strategy") == "kelly_config":
            if prob_for_meta_amount_calc > 0: # 只有在有利概率大于0时才计算凯利
                print(f"  元模型金额计算 (凯利): 方向={actual_signal_for_sound_and_simulator}, Meta有利概率={prob_for_meta_amount_calc:.3f}")
                trade_amount_to_send_float = calculate_trade_amount_from_strategy(
                    meta_static_config_for_kelly, # 包含静态默认凯利参数的字典
                    actual_signal_for_sound_and_simulator,
                    prob_for_meta_amount_calc
                )

                # 🎯 记录元模型交易开仓到动态胜率跟踪器
                try:
                    from .dynamic_win_rate_tracker import get_dynamic_win_rate_tracker
                    win_rate_tracker = get_dynamic_win_rate_tracker()

                    # 生成元模型交易ID
                    meta_trade_id = f"meta_model_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

                    # 记录元模型交易开仓
                    win_rate_tracker.record_trade_entry(
                        trade_id=meta_trade_id,
                        direction=actual_signal_for_sound_and_simulator,
                        amount=trade_amount_to_send_float,
                        target_name="meta_model"
                    )

                    print(f"[元模型] 已记录交易开仓: {meta_trade_id} ({actual_signal_for_sound_and_simulator}, ${trade_amount_to_send_float:.2f})")

                except Exception as e_meta_trade_record:
                    print(f"[元模型] 记录交易开仓失败: {e_meta_trade_record}")
                    # 不影响主流程，继续执行
            else:
                print(f"  元模型金额计算 (凯利): 有利概率为0或负 ({prob_for_meta_amount_calc:.3f})，不计算凯利金额。")
                trade_amount_to_send_float = 0.0 # 确保金额为0
        else: # 如果元模型配置了其他金额策略（例如固定），则按那种策略处理
            fixed_amount_meta = float(meta_static_config_for_kelly.get('fixed_trade_amount', 5.0)) # 示例
            trade_amount_to_send_float = fixed_amount_meta
            print(f"  元模型金额计算 (非凯利): 使用金额 {trade_amount_to_send_float:.2f}")

        # 🎯 冷却时间检查
        if trade_amount_to_send_float > 0:
            last_sent_t_meta = global_prediction_state_manager.get_last_signal_sent_time(meta_model_virtual_target_name)
            last_sent_sig_type_meta = global_prediction_state_manager.get_last_signal_type_sent(meta_model_virtual_target_name)
            cooldown_seconds_meta = getattr(config, 'META_MODEL_SIGNAL_COOLDOWN_SECONDS', 120) # 可配置的冷却

            if last_sent_sig_type_meta != actual_signal_for_sound_and_simulator or \
               (time.time() - last_sent_t_meta) >= cooldown_seconds_meta :
                should_send_signal_this_time_meta = True

        # 🎯 播放声音提醒
        if should_send_signal_this_time_meta:
            play_signal_alert_sound(actual_signal_for_sound_and_simulator)

    # 🎯 实际发送信号
    if should_send_signal_this_time_meta:
        symbol_for_meta_signal = getattr(config, 'SYMBOL', 'BTCUSDT')
        target_name_for_comm_meta = f"{meta_model_virtual_target_name}_Signal" # 使用虚拟目标名

        print(f"元模型 [{target_name_for_comm_meta}]: 发送信号. 类型: {actual_signal_for_sound_and_simulator}, 金额: {trade_amount_to_send_float:.2f}")
        if SEND_SIGNALS_TO_SIMULATOR:
            _notify_simulator(actual_signal_for_sound_and_simulator, target_name_for_comm_meta, trade_amount_to_send_float, symbol_for_meta_signal, simulator_actual_url)
        SEND_TO_COMMAND_SERVER_ENABLED = getattr(config, 'SEND_TO_COMMAND_SERVER_ENABLED', True)
        if SEND_TO_COMMAND_SERVER_ENABLED:
            payload_cs_meta = { "signal_type": actual_signal_for_sound_and_simulator, "amount": trade_amount_to_send_float, "symbol": symbol_for_meta_signal, "target_name": target_name_for_comm_meta }
            try:
                COMMAND_SERVER_URL_GLOBAL = getattr(config, 'COMMAND_SERVER_URL', "http://127.0.0.1:8080/internal_signal")
                requests.post(COMMAND_SERVER_URL_GLOBAL, json=payload_cs_meta, timeout=3).raise_for_status()
                print(f"  元模型信号成功发送至 CommandServer.")
            except Exception as e_cs_send_meta:
                print(f"!!! 元模型发送信号至 CommandServer 失败: {e_cs_send_meta}")

        # 🎯 更新状态管理器
        global_prediction_state_manager.set_last_signal_sent_time(meta_model_virtual_target_name, time.time())
        global_prediction_state_manager.set_last_signal_type_sent(meta_model_virtual_target_name, actual_signal_for_sound_and_simulator)

        # 更新元模型的凯利保守期计数器 (如果启用)
        if meta_dynamic_params.get('enable_initial_conservative_betting', False) and \
           meta_static_config_for_kelly.get("trade_amount_strategy") == "kelly_config":
            global_prediction_state_manager.increment_strategy_execution_counter(meta_model_virtual_target_name)


    # --- 开始构建元模型GUI显示文本 ---
    meta_gui_target_name = getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', "MetaSignal_BTC") 
    pred_time_gui_str_meta = datetime.now(app_timezone_param if app_timezone_param else timezone.utc).strftime("%H:%M:%S %Z")
    symbol_for_meta_price_display = getattr(config, 'SYMBOL', 'BTCUSDT')
    current_price_val_gui_meta = get_real_time_price(binance_client, symbol_override=symbol_for_meta_price_display)
    price_str_gui_meta = f"${current_price_val_gui_meta:.2f}" if current_price_val_gui_meta is not None else "$----.--"
    
    result_txt_parts_meta = [
        f"元模型综合决策 时间: {pred_time_gui_str_meta}",
        f"当前 {symbol_for_meta_price_display} 价格: {price_str_gui_meta}",
        f"------------------------------------",
        f"基础模型输入 (来自 {config.BASE_MODELS_FOR_META[0]} & {config.BASE_MODELS_FOR_META[1]}):"
    ]
    
    base_model_1_name = config.BASE_MODELS_FOR_META[0] 
    base_model_2_name = config.BASE_MODELS_FOR_META[1]
    
    # --- 处理并添加第一个基础模型的信息 ---
    core_info_m1 = all_core_infos_from_bases.get(base_model_1_name, {"p_up": np.nan, "internal_signal": "字典中未找到M1", "error": True}) 
    if core_info_m1.get("error", True):
        prob_m1_display_str = "错误(m1e)" 
        sig_m1_display_str = core_info_m1.get("internal_signal", "获取失败(m1e)")
    else:
        prob_m1_val = core_info_m1.get("p_up", np.nan) 
        sig_m1_display_str = core_info_m1.get("internal_signal", "N/A(m1s)")
        prob_m1_display_str = f"{prob_m1_val:.3f}" if not pd.isna(prob_m1_val) else "N/A(m1s)"
    result_txt_parts_meta.append(f"  {base_model_1_name}: P(UP)={prob_m1_display_str} (初步信号: {sig_m1_display_str})")

    # --- 处理并添加第二个基础模型的信息 ---
    core_info_m2 = all_core_infos_from_bases.get(base_model_2_name, {"p_down": np.nan, "internal_signal": "字典中未找到M2", "error": True}) 
    if core_info_m2.get("error", True):
        prob_m2_display_str = "错误(m2e)"
        sig_m2_display_str = core_info_m2.get("internal_signal", "获取失败(m2e)")
    else:
        prob_m2_val = core_info_m2.get("p_down", np.nan) 
        sig_m2_display_str = core_info_m2.get("internal_signal", "N/A(m2s)")
        prob_m2_display_str = f"{prob_m2_val:.3f}" if not pd.isna(prob_m2_val) else "N/A(m2s)"
    result_txt_parts_meta.append(f"  {base_model_2_name}: P(DOWN)={prob_m2_display_str} (初步信号: {sig_m2_display_str})")
    
    result_txt_parts_meta.append(f"------------------------------------") # 分隔线

    # --- 添加预过滤判断的详细文本 ---
    if pre_filter_triggered_flag:
        if pd.isna(prob_up_m1_for_filter) or pd.isna(prob_down_m2_for_filter):
            result_txt_parts_meta.append(f"预过滤判断: 基础概率存在NaN (P_UP_M1={prob_up_m1_for_filter}, P_DOWN_M2={prob_down_m2_for_filter})")
            result_txt_parts_meta.append(f"           结果 -> 中性 (因概率获取问题)")
        else:
            result_txt_parts_meta.append(
                f"预过滤判断: P(UP)_M1 ({prob_up_m1_for_filter:.3f}) < {conf_thresh_up} AND P(DOWN)_M2 ({prob_down_m2_for_filter:.3f}) < {conf_thresh_down}"
            )
            result_txt_parts_meta.append(f"           结果 -> 中性 (预过滤)")
    else: # 如果没有被预过滤，说明是传递给元模型了
        result_txt_parts_meta.append(
             f"预过滤判断: P(UP)_M1 ({prob_up_m1_for_filter:.3f}) 或 P(DOWN)_M2 ({prob_down_m2_for_filter:.3f}) 未同时低于阈值。"
        ) # 可以在这里加上阈值 conf_thresh_up, conf_thresh_down
        result_txt_parts_meta.append(f"           结果 -> 已传递给元模型进行决策")
    # --- 预过滤展示结束 ---

    result_txt_parts_meta.extend([
        f"------------------------------------",
        f"元模型输出: {prediction_label_meta}", # 这个 prediction_label_meta 会根据是否预过滤或元模型实际预测而变化
        f"  Meta Probas -> [下跌:{meta_model_decision_probas[0]:.2%}, 上涨:{meta_model_decision_probas[1]:.2%}, 中性:{meta_model_decision_probas[2]:.2%}]"
    ])

    # --- 最终决策的显示 (这个逻辑与您提供的片段一致) ---
    if should_send_signal_this_time_meta and trade_amount_to_send_float > 0:
        result_txt_parts_meta.append(f"  最终决策: {actual_signal_for_sound_and_simulator}, 计划金额: {trade_amount_to_send_float:.2f}")
    elif actual_signal_for_sound_and_simulator: 
         result_txt_parts_meta.append(f"  最终决策: {actual_signal_for_sound_and_simulator} (但未发送)") # 简化提示
    else: 
         result_txt_parts_meta.append(f"  最终决策: 观望")



    if not is_master_signal_sending_enabled and actual_signal_for_sound_and_simulator:
        result_txt_parts_meta.append(f"  (注意: 主信号发送已禁用)")
    if not is_meta_signal_dynamically_enabled and actual_signal_for_sound_and_simulator: # is_meta_signal_dynamically_enabled 从元模型的动态参数获取
        result_txt_parts_meta.append(f"  (注意: 元模型信号在动态参数中被禁用)")

    result_text_final_for_gui_meta = "\n".join(result_txt_parts_meta)
    if hasattr(gui, 'update_prediction_display') and gui._gui_root and gui._gui_root.winfo_exists():
        gui.update_gui_safe(gui.update_prediction_display, meta_gui_target_name, result_text_final_for_gui_meta, prediction_color_meta if 'prediction_color_meta' in locals() else config.NEUTRAL_COLOR)
        status_msg_meta = f"元模型: {prediction_label_meta.split('(')[0].strip()}"
        status_level_meta = "info" # ... (根据 final_meta_signal 设置 status_level_meta)
        if "Error" in final_meta_signal: status_level_meta = "error"
        elif "UP_Meta" in final_meta_signal: status_level_meta = config.UP_COLOR
        # ...
        gui.update_gui_safe(gui.update_status, status_msg_meta, status_level_meta)

    print(f"--- 元模型预测周期结束: {final_meta_signal} ---")
    return True, final_meta_signal




# --- 计划预测任务 (与你原始版本结构一致) ---
